import { useState, useCallback } from 'react';
import {
  BangTinhKhauHaoTSCDTTItem,
  BangTinhKhauHaoTSCDTTResponse,
  UseBangTinhKhauHaoTSCDTTReturn
} from '@/types/schemas';
import { SearchFormValues } from '../schema';
import api from '@/lib/api';

export type BangTinhKhauHaoTSCDTTSearchFormValues = SearchFormValues;

const generateMockData = (): BangTinhKhauHaoTSCDTTItem[] => {
  return [
    {
      id: '1',
      ma_ts: 'TS001',
      ten_ts: '<PERSON><PERSON><PERSON> tính xách tay Dell Inspiron 15',
      ten_dvt: 'Chiếc',
      so_luong: 1,
      ma_lts: 'LTS001',
      ten_lts: 'Thiết bị văn phòng',
      ngay_kh0: '2025-01-15',
      so_ky_kh: 36,
      ngay_kh_kt: '2028-01-15',
      tk_ts: '211',
      ma_bp: 'BP001',
      systotal: 25000000
    },
    {
      id: '2',
      ma_ts: 'TS002',
      ten_ts: '<PERSON><PERSON><PERSON> <PERSON><PERSON> 27 inch',
      ten_dvt: 'Chiếc',
      so_luong: 2,
      ma_lts: 'LTS001',
      ten_lts: 'Thiết bị văn phòng',
      ngay_kh0: '2025-01-16',
      so_ky_kh: 24,
      ngay_kh_kt: '2027-01-16',
      tk_ts: '211',
      ma_bp: 'BP001',
      systotal: 8500000
    },
    {
      id: '3',
      ma_ts: 'TS003',
      ten_ts: 'Server Dell PowerEdge R740',
      ten_dvt: 'Bộ',
      so_luong: 1,
      ma_lts: 'LTS002',
      ten_lts: 'Máy móc thiết bị',
      ngay_kh0: '2025-01-20',
      so_ky_kh: 60,
      ngay_kh_kt: '2030-01-20',
      tk_ts: '211',
      ma_bp: 'BP001',
      systotal: 150000000
    },
    {
      id: '4',
      ma_ts: 'TS004',
      ten_ts: 'Bàn làm việc gỗ cao cấp',
      ten_dvt: 'Bộ',
      so_luong: 5,
      ma_lts: 'LTS003',
      ten_lts: 'Nội thất văn phòng',
      ngay_kh0: '2025-02-01',
      so_ky_kh: 120,
      ngay_kh_kt: '2035-02-01',
      tk_ts: '211',
      ma_bp: 'BP002',
      systotal: 12000000
    },
    {
      id: '5',
      ma_ts: 'TS005',
      ten_ts: 'Máy cắt laser CNC',
      ten_dvt: 'Máy',
      so_luong: 1,
      ma_lts: 'LTS002',
      ten_lts: 'Máy móc thiết bị',
      ngay_kh0: '2025-02-15',
      so_ky_kh: 84,
      ngay_kh_kt: '2032-02-15',
      tk_ts: '211',
      ma_bp: 'BP003',
      systotal: 450000000
    },
    {
      id: '6',
      ma_ts: 'TS006',
      ten_ts: 'Xe tải Hyundai HD120SL',
      ten_dvt: 'Chiếc',
      so_luong: 1,
      ma_lts: 'LTS004',
      ten_lts: 'Phương tiện vận tải',
      ngay_kh0: '2025-03-01',
      so_ky_kh: 72,
      ngay_kh_kt: '2031-03-01',
      tk_ts: '211',
      ma_bp: 'BP004',
      systotal: 850000000
    },
    {
      id: '7',
      ma_ts: 'TS007',
      ten_ts: 'Máy tính cũ Pentium 4',
      ten_dvt: 'Chiếc',
      so_luong: 1,
      ma_lts: 'LTS001',
      ten_lts: 'Thiết bị văn phòng',
      ngay_kh0: '2020-03-10',
      so_ky_kh: 36,
      ngay_kh_kt: '2023-03-10',
      tk_ts: '211',
      ma_bp: 'BP001',
      systotal: 5000000
    },
    {
      id: '8',
      ma_ts: 'TS008',
      ten_ts: 'Kính hiển vi điện tử',
      ten_dvt: 'Bộ',
      so_luong: 1,
      ma_lts: 'LTS005',
      ten_lts: 'Thiết bị nghiên cứu',
      ngay_kh0: '2025-04-01',
      so_ky_kh: 120,
      ngay_kh_kt: '2035-04-01',
      tk_ts: '211',
      ma_bp: 'BP005',
      systotal: 280000000
    }
  ];
};

/**
 * Custom hook for managing BangTinhKhauHaoTSCDTT (Monthly Fixed Asset Depreciation Calculation) data
 *
 * This hook provides functionality to fetch monthly fixed asset depreciation calculation data
 * with mock support for testing and development purposes.
 */
export function useBangTinhKhauHaoTSCDTT(
  searchParams: BangTinhKhauHaoTSCDTTSearchFormValues
): UseBangTinhKhauHaoTSCDTTReturn {
  const [data, setData] = useState<BangTinhKhauHaoTSCDTTItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BangTinhKhauHaoTSCDTTSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BangTinhKhauHaoTSCDTTResponse>(
        '/tai-san/tang-giam-tscd/bang-tinh-khau-hao-tscd-theo-thang/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
