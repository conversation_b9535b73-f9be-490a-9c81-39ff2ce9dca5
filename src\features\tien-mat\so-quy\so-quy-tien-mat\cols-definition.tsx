import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { DateRangePresets } from '@/components/arito/arito-range-select';

export const cashBookColumns: GridColDef[] = [
  {
    field: 'don_vi',
    headerName: 'Đơn vị',
    width: 90
  },
  {
    field: 'ngay_chung_tu',
    headerName: 'Ngày c/từ',
    width: 110,
    type: 'date',
    valueGetter: (value: any) => {
      if (!value || typeof value !== 'string') return null;
      // Convert DD/MM/YYYY string to Date object
      const [day, month, year] = (value as string).split('/');
      return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    }
  },
  {
    field: 'so_chung_tu',
    headerName: 'Số c/từ',
    width: 110
  },
  {
    field: 'ma_khach_hang',
    headerName: '<PERSON><PERSON> khách hàng',
    width: 130
  },
  {
    field: 'ten_khach_hang',
    headerName: 'Tên khách hàng',
    width: 180
  },
  {
    field: 'dien_giai',
    headerName: '<PERSON>ễn giải',
    width: 200
  },
  {
    field: 'tk_doi_ung',
    headerName: 'TK đối ứng',
    width: 100
  },
  {
    field: 'ngoai_te',
    headerName: 'Ngoại tệ',
    width: 100,
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'ty_gia',
    headerName: 'Tỷ giá',
    width: 100,
    align: 'right',
    headerAlign: 'right',
    renderCell: (params: GridRenderCellParams) => {
      const value = params.value;
      if (value === null || value === undefined) return '';
      return new Intl.NumberFormat('vi-VN').format(value);
    }
  },
  {
    field: 'ps_no',
    headerName: 'PS nợ',
    width: 120,
    align: 'right',
    headerAlign: 'right',
    renderCell: (params: GridRenderCellParams) => {
      const value = params.value;
      if (value === null || value === undefined) return '';
      return new Intl.NumberFormat('vi-VN').format(value);
    }
  },
  {
    field: 'ps_co',
    headerName: 'PS có',
    width: 120,
    align: 'right',
    headerAlign: 'right',
    renderCell: (params: GridRenderCellParams) => {
      const value = params.value;
      if (value === null || value === undefined) return '';
      return new Intl.NumberFormat('vi-VN').format(value);
    }
  },
  {
    field: 'so_du',
    headerName: 'Số dư',
    width: 120,
    align: 'right',
    headerAlign: 'right',
    renderCell: (params: GridRenderCellParams) => {
      const value = params.value;
      if (value === null || value === undefined) return '';
      return new Intl.NumberFormat('vi-VN').format(value);
    }
  },
  {
    field: 'bo_phan',
    headerName: 'Bộ phận',
    width: 100
  },
  {
    field: 'vu_viec',
    headerName: 'Vụ việc',
    width: 100
  },
  {
    field: 'hop_dong',
    headerName: 'Hợp đồng',
    width: 110
  },
  {
    field: 'dot_thanh_toan',
    headerName: 'Đợt thanh toán',
    width: 130
  },
  {
    field: 'khe_uoc',
    headerName: 'Khế ước',
    width: 100
  },
  {
    field: 'phi',
    headerName: 'Phí',
    width: 90
  },
  {
    field: 'san_pham',
    headerName: 'Sản phẩm',
    width: 120
  },
  {
    field: 'lenh_san_xuat',
    headerName: 'Lệnh sản xuất',
    width: 130
  },
  {
    field: 'cp_khong_hop_le',
    headerName: 'C/p không h/lệ',
    width: 140
  },
  {
    field: 'ma_chung_tu',
    headerName: 'Mã c/từ',
    width: 100
  }
];

export const getFixedRowsData = () => [
  {
    id: '1',
    chi_tieu: 'Đầu kỳ',
    isGroup: true
  },
  {
    id: '2',
    chi_tieu: 'Tổng phát sinh',
    isGroup: true
  },
  {
    id: '3',
    chi_tieu: 'Cuối kỳ',
    isGroup: true
  }
];

export const renderCell = (params: any) => {
  const { row, field, value } = params;

  if (field === 'chi_tieu') {
    return (
      <div
        style={{
          fontWeight: row.isGroup ? 'bold' : 'normal',
          paddingLeft: row.id.length > 2 ? '20px' : '0px'
        }}
      >
        {value}
      </div>
    );
  }

  return value?.toLocaleString() || '';
};

export const DEFAULT_PRESETS: DateRangePresets = {
  today: 'Hôm nay',
  thisWeek: 'Tuần này',
  thisMonth: 'Tháng này',
  currentToDate: 'Đầu tháng đến hiện tại',
  prevMonth: 'Tháng trước',
  nextMonth: 'Tháng sau',
  month1: 'Tháng 1',
  month2: 'Tháng 2',
  month3: 'Tháng 3',
  month4: 'Tháng 4',
  month5: 'Tháng 5',
  month6: 'Tháng 6',
  month7: 'Tháng 7',
  month8: 'Tháng 8',
  month9: 'Tháng 9',
  month10: 'Tháng 10',
  month11: 'Tháng 11',
  month12: 'Tháng 12',
  quarter1: 'Quý 1',
  quarter2: 'Quý 2',
  quarter3: 'Quý 3',
  quarter4: 'Quý 4',
  thisQuarter: 'Quý này',
  prevQuarter: 'Quý trước',
  quarterToDate: 'Đầu quý đến hiện tại',
  thisYear: 'Năm nay',
  prevYear: 'Năm trước',
  yearToDate: 'Đầu năm đến hiện tại'
};

export const accountSearchColumns = [
  { field: 'ma_tai_khoan', headerName: 'Mã tài khoản', width: 100 },
  { field: 'ten_tai_khoan', headerName: 'Tên tài khoản', width: 300 },
  { field: 'tai_khoan_me', headerName: 'Tài khoản mẹ', width: 100 },
  { field: 'tk_so_cai', headerName: 'Tk số cái', width: 100 },
  { field: 'tk_chi_tiet', headerName: 'Tk chi tiết', width: 100 },
  { field: 'bac_tk', headerName: 'Bậc tk', width: 100 }
];

export const exportPrintColColums: GridColDef[] = [
  { field: 'col', headerName: 'Cột', width: 150 },
  { field: 'colName', headerName: 'Tên cột', width: 150 },
  { field: 'colNameEng', headerName: 'Tên tiếng anh', width: 150 },
  { field: 'colWidth', headerName: 'Độ rộng', width: 150 },
  { field: 'colType', headerName: 'Định dạng', width: 150 },
  { field: 'colAlign', headerName: 'Căn chỉnh', width: 150 },
  { field: 'colBold', headerName: 'In đậm', width: 150 },
  { field: 'colItalic', headerName: 'In nghiêng', width: 150 },
  { field: 'colSum', headerName: 'Tổng', width: 150 }
];
