import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

export const soChiTietCongNoTieuChuanColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  { field: 'don_vi', headerName: 'Đơn vị', width: 100 },
  { field: 'ngay_ctu', headerName: 'Ngày c/từ', width: 150 },
  {
    field: 'so_ctu',
    headerName: 'Số c/từ',
    width: 200,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row)}>
        {params.value}
      </div>
    )
  },
  { field: 'ngay_hoa_don', headerName: '<PERSON><PERSON>y hóa đơn', width: 150 },
  { field: 'so_hoa_don', headerName: 'Số hóa đơn', width: 150 },
  { field: 'tai_khoan', headerName: 'Tài khoản', width: 100 },
  { field: 'tk_doi_ung', headerName: 'Tk đối ứng', width: 100 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 200 },
  {
    field: 'ps_no',
    headerName: 'Ps nợ',
    width: 100,
    align: 'right',
    headerAlign: 'right',
    renderCell: (params: GridRenderCellParams) => {
      const value = params.value;
      if (value === null || value === undefined) return '';
      return new Intl.NumberFormat('vi-VN').format(value);
    }
  },
  {
    field: 'ps_co',
    headerName: 'Ps có',
    width: 100,
    align: 'right',
    headerAlign: 'right',
    renderCell: (params: GridRenderCellParams) => {
      const value = params.value;
      if (value === null || value === undefined) return '';
      return new Intl.NumberFormat('vi-VN').format(value);
    }
  },
  { field: 'ma_ctu', headerName: 'Mã c/từ', width: 100 }
];

export const soChiTietCongNoNgoaiTeColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  { field: 'don_vi', headerName: 'Đơn vị', width: 100 },
  { field: 'ngay_ctu', headerName: 'Ngày c/từ', width: 150 },
  {
    field: 'so_ctu',
    headerName: 'Số c/từ',
    width: 15,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row)}>
        {params.value}
      </div>
    )
  },
  { field: 'ngay_hoa_don', headerName: 'Ngày hóa đơn', width: 150 },
  { field: 'so_hoa_don', headerName: 'Số hóa đơn', width: 150 },
  { field: 'tai_khoan', headerName: 'Tài khoản', width: 100 },
  { field: 'tk_doi_ung', headerName: 'Tk đối ứng', width: 100 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 200 },
  {
    field: 'ps_no_nt',
    headerName: 'Ps nợ nt',
    width: 100,
    align: 'right',
    headerAlign: 'right',
    renderCell: (params: GridRenderCellParams) => {
      const value = params.value;
      if (value === null || value === undefined) return '';
      return new Intl.NumberFormat('vi-VN').format(value);
    }
  },
  {
    field: 'ps_co_nt',
    headerName: 'Ps có nt',
    width: 100,
    align: 'right',
    headerAlign: 'right',
    renderCell: (params: GridRenderCellParams) => {
      const value = params.value;
      if (value === null || value === undefined) return '';
      return new Intl.NumberFormat('vi-VN').format(value);
    }
  },
  {
    field: 'ps_no',
    headerName: 'Ps nợ',
    width: 100,
    align: 'right',
    headerAlign: 'right',
    renderCell: (params: GridRenderCellParams) => {
      const value = params.value;
      if (value === null || value === undefined) return '';
      return new Intl.NumberFormat('vi-VN').format(value);
    }
  },
  {
    field: 'ps_co',
    headerName: 'Ps có',
    width: 100,
    align: 'right',
    headerAlign: 'right',
    renderCell: (params: GridRenderCellParams) => {
      const value = params.value;
      if (value === null || value === undefined) return '';
      return new Intl.NumberFormat('vi-VN').format(value);
    }
  },
  { field: 'ma_ctu', headerName: 'Mã c/từ', width: 100 }
];
