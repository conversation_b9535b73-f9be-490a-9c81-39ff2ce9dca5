import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { Checkbox } from '@/components/ui/checkbox';

export const customerGroupSearchColumns: GridColDef[] = [
  {
    field: 'customerGroupCode',
    headerName: 'Mã nhóm khách hàng',
    width: 150
  },
  {
    field: 'customerGroupName',
    headerName: 'Tên nhóm khách hàng',
    width: 250
  }
];

export const productSearchColumns: GridColDef[] = [
  {
    field: 'ma_vt',
    headerName: 'Mã vật tư',
    width: 120
  },
  {
    field: 'ten_vt',
    headerName: 'Tên vật tư',
    width: 250
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.dvt_data.dvt || '';
    }
  },
  {
    field: 'nhom_vt1',
    headerName: 'Nhóm 1',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      // return params.row.nhom_vt1_data.ma_nhom || '';
    }
  },
  {
    field: 'lo_yn',
    headerName: 'Theo dõi lô',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'qc_yn',
    headerName: 'Quy cách',
    width: 150,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'hinh_anh',
    headerName: 'Hình ảnh',
    width: 120
  }
];

export const itemTypeSearchColumns = [
  { field: 'loai_vat_tu', headerName: 'Loại vật tư', width: 120 },
  { field: 'ten_loai', headerName: 'Tên loại vật tư', width: 280 }
];

export const productGroupSearchColumns: GridColDef[] = [
  {
    field: 'ma_nh',
    headerName: 'Mã nhóm vật tư',
    width: 150
  },
  {
    field: 'ten_nh',
    headerName: 'Tên nhóm vật tư',
    width: 250
  }
];

export const warehouseSearchColumns: GridColDef[] = [
  { field: 'ma_kho', headerName: 'Mã kho', width: 120 },
  { field: 'ten_kho', headerName: 'Tên kho', width: 250 },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 120 },
  {
    field: 'vi_tri_yn',
    headerName: 'Theo dõi vị trí',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  }
];

export const transactionSearchColumns: GridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    sortable: false,
    disableColumnMenu: true
  },
  {
    field: 'transactionCode',
    headerName: 'Mã giao dịch',
    width: 120
  },
  {
    field: 'transactionName',
    headerName: 'Tên giao dịch',
    width: 200
  },
  {
    field: 'documentCode',
    headerName: 'Mã chứng từ',
    width: 120
  }
];

export const batchSearchColumns: GridColDef[] = [
  {
    field: 'ma_lo',
    headerName: 'Mã lô',
    width: 120
  },
  {
    field: 'ten_lo',
    headerName: 'Tên lô',
    width: 250
  }
];

export const locationSearchColumns: GridColDef[] = [
  {
    field: 'ma_vi_tri',
    headerName: 'Mã vị trí',
    width: 120
  },
  {
    field: 'ten_vi_tri',
    headerName: 'Tên vị trí',
    width: 250
  }
];

export const customerSearchColumns: GridColDef[] = [
  { field: 'customer_code', headerName: 'Mã đối tượng', width: 150 },
  { field: 'customer_name', headerName: 'Tên đối tượng', width: 200 },
  { field: 'CongNoPhaiThu', headerName: 'Công nợ p/thu', width: 150 },
  { field: 'CongNoPhaiTra', headerName: 'Công nợ p/trả', width: 150 },
  { field: 'tax_code', headerName: 'Mã số thuế', width: 150 },
  { field: 'email', headerName: 'Email', width: 200 },
  { field: 'phone', headerName: 'Số điện thoại', width: 150 }
];
