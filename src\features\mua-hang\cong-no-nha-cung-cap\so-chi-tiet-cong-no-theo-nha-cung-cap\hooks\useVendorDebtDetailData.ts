import { useState, useCallback, useEffect } from 'react';
import { SearchFormValues } from '../schemas';
import api from '@/lib/api';

export interface VendorDebtDetailItem {
  id: string;
  don_vi: string;
  ngay_ctu: string;
  so_ctu: string;
  ngay_hoa_don: string;
  so_hoa_don: string;
  tai_khoan: string;
  tk_doi_ung: string;
  dien_giai: string;
  ps_no: number;
  ps_co: number;
  ma_ctu: string;
}

export interface VendorDebtDetailResponse {
  results: VendorDebtDetailItem[];
  total: number;
}

export interface UseVendorDebtDetailReturn {
  data: VendorDebtDetailItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

const generateMockData = (): VendorDebtDetailItem[] => {
  return [
    {
      id: '1',
      don_vi: 'CN001',
      ngay_ctu: '01/04/2025',
      so_ctu: 'PN001',
      ngay_hoa_don: '01/04/2025',
      so_hoa_don: 'HD001',
      tai_khoan: '331',
      tk_doi_ung: '111',
      dien_giai: 'Mua hàng từ nhà cung cấp ABC',
      ps_no: 15000000,
      ps_co: 0,
      ma_ctu: 'MCT001'
    },
    {
      id: '2',
      don_vi: 'CN001',
      ngay_ctu: '02/04/2025',
      so_ctu: 'PC001',
      ngay_hoa_don: '02/04/2025',
      so_hoa_don: 'HD002',
      tai_khoan: '111',
      tk_doi_ung: '331',
      dien_giai: 'Thanh toán tiền hàng cho NCC ABC',
      ps_no: 0,
      ps_co: 10000000,
      ma_ctu: 'MCT002'
    },
    {
      id: '3',
      don_vi: 'CN001',
      ngay_ctu: '03/04/2025',
      so_ctu: 'PN002',
      ngay_hoa_don: '03/04/2025',
      so_hoa_don: 'HD003',
      tai_khoan: '331',
      tk_doi_ung: '111',
      dien_giai: 'Mua nguyên vật liệu từ NCC XYZ',
      ps_no: 8500000,
      ps_co: 0,
      ma_ctu: 'MCT003'
    },
    {
      id: '4',
      don_vi: 'CN002',
      ngay_ctu: '04/04/2025',
      so_ctu: 'PN003',
      ngay_hoa_don: '04/04/2025',
      so_hoa_don: 'HD004',
      tai_khoan: '331',
      tk_doi_ung: '152',
      dien_giai: 'Mua thiết bị văn phòng',
      ps_no: 12000000,
      ps_co: 0,
      ma_ctu: 'MCT004'
    },
    {
      id: '5',
      don_vi: 'CN002',
      ngay_ctu: '05/04/2025',
      so_ctu: 'PC002',
      ngay_hoa_don: '05/04/2025',
      so_hoa_don: 'HD005',
      tai_khoan: '112',
      tk_doi_ung: '331',
      dien_giai: 'Chuyển khoản thanh toán NCC DEF',
      ps_no: 0,
      ps_co: 7500000,
      ma_ctu: 'MCT005'
    },
    {
      id: '6',
      don_vi: 'CN001',
      ngay_ctu: '06/04/2025',
      so_ctu: 'PN004',
      ngay_hoa_don: '06/04/2025',
      so_hoa_don: 'HD006',
      tai_khoan: '331',
      tk_doi_ung: '111',
      dien_giai: 'Mua hàng hóa kinh doanh',
      ps_no: 25000000,
      ps_co: 0,
      ma_ctu: 'MCT006'
    },
    {
      id: '7',
      don_vi: 'CN003',
      ngay_ctu: '07/04/2025',
      so_ctu: 'PC003',
      ngay_hoa_don: '07/04/2025',
      so_hoa_don: 'HD007',
      tai_khoan: '111',
      tk_doi_ung: '331',
      dien_giai: 'Thanh toán tiền mặt cho NCC GHI',
      ps_no: 0,
      ps_co: 5000000,
      ma_ctu: 'MCT007'
    },
    {
      id: '8',
      don_vi: 'CN001',
      ngay_ctu: '08/04/2025',
      so_ctu: 'PN005',
      ngay_hoa_don: '08/04/2025',
      so_hoa_don: 'HD008',
      tai_khoan: '331',
      tk_doi_ung: '152',
      dien_giai: 'Mua máy móc thiết bị',
      ps_no: 45000000,
      ps_co: 0,
      ma_ctu: 'MCT008'
    },
    {
      id: '9',
      don_vi: 'CN002',
      ngay_ctu: '09/04/2025',
      so_ctu: 'PC004',
      ngay_hoa_don: '09/04/2025',
      so_hoa_don: 'HD009',
      tai_khoan: '112',
      tk_doi_ung: '331',
      dien_giai: 'Thanh toán qua ngân hàng',
      ps_no: 0,
      ps_co: 20000000,
      ma_ctu: 'MCT009'
    },
    {
      id: '10',
      don_vi: 'CN003',
      ngay_ctu: '10/04/2025',
      so_ctu: 'PN006',
      ngay_hoa_don: '10/04/2025',
      so_hoa_don: 'HD010',
      tai_khoan: '331',
      tk_doi_ung: '111',
      dien_giai: 'Mua dịch vụ bảo trì',
      ps_no: 3500000,
      ps_co: 0,
      ma_ctu: 'MCT010'
    }
  ];
};

const useVendorDebtDetailData = (): UseVendorDebtDetailReturn => {
  const [data, setData] = useState<VendorDebtDetailItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastSearchParams, setLastSearchParams] = useState<SearchFormValues | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<VendorDebtDetailResponse>(
        '/mua-hang/cong-no-nha-cung-cap/so-chi-tiet-cong-no-theo-nha-cung-cap/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
      setLastSearchParams(searchParams);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    if (lastSearchParams) {
      await fetchData(lastSearchParams);
    }
  }, [fetchData, lastSearchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
};

export default useVendorDebtDetailData;
