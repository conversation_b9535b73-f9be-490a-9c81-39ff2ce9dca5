import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, type <PERSON>aiTSCDCCDC } from '@/types/schemas';

/**
 * Hook for managing search field states in the bao-cao-chi-tiet-tang-giam-tscd feature
 *
 * @returns Object with states and setters for search fields
 */
export const useSearchFieldStates = () => {
  const [taiSan, setTaiSan] = useState<any | null>(null);
  const [loaiTaiSan, setLoaiTaiSan] = useState<LoaiTSCDCCDC | null>(null);

  const [bo<PERSON>han, setBo<PERSON>han] = useState<BoPhan | null>(null);

  const [nhom1, setNhom1] = useState<any | null>(null);
  const [nhom2, setNhom2] = useState<any | null>(null);
  const [nhom3, setNhom3] = useState<any | null>(null);
  const searchFieldStates = {
    taiSan,
    setTaiSan,
    loaiTaiSan,
    setLoaiTaiSan,
    boP<PERSON>,
    setB<PERSON><PERSON><PERSON>,
    nhom1,
    setNhom1,
    nhom2,
    setNhom2,
    nhom3,
    setNhom3
  };
  return searchFieldStates;
};
