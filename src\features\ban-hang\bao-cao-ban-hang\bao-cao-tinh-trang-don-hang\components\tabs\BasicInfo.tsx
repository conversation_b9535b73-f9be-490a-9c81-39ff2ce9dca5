import DocumentNumberRange from '@/components/custom/arito/form/search-fields/DocumentNumberRangeField';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { Label } from '@/components/ui/label';

const BasicInfo: React.FC = () => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex w-[61%] items-center'>
          <Label className='w-40 min-w-40'>Ngày từ/đến:</Label>
          <div>
            <AritoFormDateRangeDropdown fromDateName='ngay_tu' toDateName='ngay_den' />
          </div>
        </div>
        <div className='flex items-center'>
          <DocumentNumberRange fromName='so_chung_tu_tu' toName='so_chung_tu_den' labelClassName='w-40 min-w-40' />
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
