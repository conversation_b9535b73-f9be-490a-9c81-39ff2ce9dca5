'use client';

import React from 'react';
import { EditPrintTemplateDialog } from '@/components/custom/arito/edit-print-template';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { ActionBar, SearchDialog } from './components';
import { initialSearchValues } from './schema';
import { useReportData } from './hooks';

export default function BangKeChungTuPage() {
  const {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate,

    tables,
    handleRowClick,
    isLoading,

    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick
  } = useReportData();

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {initialSearchDialogOpen && (
        <SearchDialog
          open={initialSearchDialogOpen}
          onClose={handleInitialSearchClose}
          onSubmit={handleInitialSearch}
          formMode={'add'}
          initialData={initialSearchValues}
        />
      )}

      <EditPrintTemplateDialog
        open={editPrintTemplateDialogOpen}
        onClose={handleClosePrintTemplateDialog}
        onSave={handleSavePrintTemplate}
      />

      {isLoading && <LoadingOverlay />}
      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onPrintClick={() => {}}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportClick={handleExportDataClick}
            onEditPrintClick={handleEditPrintTemplateClick}
            searchParams={searchParams}
          />

          <div className='w-full overflow-hidden'>
            {!isLoading && <AritoDataTables tables={tables} onRowClick={handleRowClick} />}
          </div>
        </>
      )}
    </div>
  );
}
