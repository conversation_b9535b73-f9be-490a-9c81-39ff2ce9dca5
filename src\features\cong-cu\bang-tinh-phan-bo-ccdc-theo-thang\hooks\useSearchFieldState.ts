import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, type <PERSON><PERSON>TSCDCCDC, type CongCuDungCu } from '@/types/schemas';

export const useSearchFieldStates = () => {
  const [congCu, setCongCu] = useState<CongCuDungCu | null>(null);
  const [loaiCongCu, setLoaiCongCu] = useState<LoaiTSCDCCDC | null>(null);
  const [boPhan, setBoPhan] = useState<BoPhan | null>(null);
  const [nhom1, setNhom1] = useState<any | null>(null);
  const [nhom2, setNhom2] = useState<any | null>(null);
  const [nhom3, setNhom3] = useState<any | null>(null);

  const searchFieldStates = {
    taiSan: congCu,
    setTaiSan: setCongCu,
    loaiTaiSan: loaiCongCu,
    setLoaiTaiSan: setLoaiCongCu,
    congCu,
    setCongCu,
    loaiCongCu,
    setLoaiCongCu,
    bo<PERSON><PERSON>,
    setBo<PERSON>han,
    nhom1,
    setNhom1,
    nhom2,
    setNhom2,
    nhom3,
    setNhom3
  };
  return searchFieldStates;
};
