import { ChangeEvent, useState } from 'react';
import { UploadCloud } from 'lucide-react';
import AttachFileTabType8 from '@/components/cac-loai-form/popup-form-type-8/AttachFileTabType8';
import { FormField } from '@/components/custom/arito/form/form-field';
import AritoIcon from '@/components/custom/arito/icon';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface Props {
  formMode: 'add' | 'edit' | 'view';
  onFileChange?: (file: File | null) => void;
}

export const OtherTab = ({ formMode, onFileChange }: Props) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
    onFileChange?.(file);
  };

  return (
    <div className='space-y-4 p-4'>
      <FormField
        label='Tên doanh nghiệp'
        className='flex items-center justify-between'
        name='enterprise_name'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
      />

      <FormField
        label='Tỉnh/Thành phố'
        className='flex items-center justify-between'
        name='province'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
      />

      <FormField
        label='Ngày sinh'
        className='flex items-center justify-between'
        name='birth_date'
        labelClassName='min-w-[100px]'
        type='date'
        disabled={formMode === 'view'}
      />

      <FormField
        label='Số CMND'
        className='flex items-center justify-between'
        name='id_number'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
      />

      <FormField
        label='Người đại diện pháp lý'
        className='flex items-center justify-between'
        name='legal_representative'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
      />

      <FormField
        label='Chức vụ người đại diện'
        className='flex items-center justify-between'
        name='representative_position'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
      />

      <FormField
        label='Từ khóa tìm kiếm'
        className='flex items-center justify-between'
        name='search_keywords'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
      />

      <FormField
        label='Số khách hàng'
        className='flex items-center justify-between'
        name='customer_number'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
      />

      {/* File Upload Section */}
      <AttachFileTabType8 formMode={formMode} onFileChange={() => {}} />
    </div>
  );
};
