import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { congCuDungCuSearchColumns } from '@/constants/search-columns';
import { FormField } from '@/components/custom/arito/form/form-field';
import type { CongCuDungCu } from '@/types/schemas';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

interface BasicInfoProps {
  searchFieldStates: {
    taiSan: CongCuDungCu | null;
    setTaiSan: (congCu: CongCuDungCu | null) => void;
  };
}

const BasicInfo: React.FC<BasicInfoProps> = ({ searchFieldStates }) => {
  const { taiSan, setTaiSan } = searchFieldStates;
  return (
    <div className='min-w-[850px] space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Từ kỳ/năm</Label>
          <div className='flex items-center gap-2'>
            <FormField name='tu_ky' type='number' className='w-20' />
            <FormField name='tu_nam' type='number' className='w-24' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Đến kỳ/năm</Label>
          <div className='flex items-center gap-2'>
            <FormField name='den_ky' type='number' className='w-20' />
            <FormField name='den_nam' type='number' className='w-24' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Công cụ</Label>
          <SearchField<CongCuDungCu>
            searchEndpoint={`/${QUERY_KEYS.CONG_CU_DUNG_CU}/`}
            searchColumns={congCuDungCuSearchColumns}
            dialogTitle='Danh mục công cụ dụng cụ'
            columnDisplay='ma_cc'
            displayRelatedField='ten_cc'
            value={taiSan?.ma_cc || ''}
            onRowSelection={setTaiSan}
          />
        </div>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Chi tiết theo</Label>
          <FormField
            name='detailBy'
            type='select'
            className='min-w-40'
            options={[
              { label: 'Công cụ', value: '1' },
              { label: 'Công cụ và bộ phận', value: '2' }
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
