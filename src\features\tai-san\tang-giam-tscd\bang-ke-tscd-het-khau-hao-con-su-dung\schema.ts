import { z } from 'zod';

export const searchSchema = z.object({
  // Basic fields
  reportDate: z.string().optional(),
  payDate: z.string().optional(),
  fromDate: z.string().optional(),
  toDate: z.string().optional(),
  accountId: z.string().optional(),

  // Details tab
  loai_tai_san: z.string().optional(),
  bo_phan_su_dung: z.string().optional(),
  nhom_tai_san: z.string().optional(),
  mau_bao_cao: z.string().optional(),

  // Other tab
  fromDocumentNumber: z.string().optional(),
  toDocumentNumber: z.string().optional(),
  reportFilterTemplate: z.string().optional(),
  reportTemplate: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  reportDate: '',
  payDate: '',
  fromDate: '',
  toDate: '',
  accountId: '',

  // Details tab
  loai_tai_san: '',
  bo_phan_su_dung: '',
  nhom_tai_san: '',
  mau_bao_cao: '0',

  // Other tab
  fromDocumentNumber: '',
  toDocumentNumber: '',
  reportFilterTemplate: '',
  reportTemplate: ''
};
