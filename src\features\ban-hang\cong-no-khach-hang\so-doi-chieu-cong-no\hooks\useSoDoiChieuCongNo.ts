import { useState, useCallback } from 'react';
import {
  SoDoiChieuCongNoItem,
  SoDoiChieuCongNoResponse,
  SoDoiChieuCongNoSearchFormValues,
  UseSoDoiChieuCongNoReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): SoDoiChieuCongNoItem[] => {
  return [
    {
      id: '1',
      ma_unit: 'CN',
      ngay_ct: '2024-01-05',
      so_ct: 'DC001',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Đ<PERSON><PERSON> chiếu công nợ nhà cung cấp ABC Technology',
      ps_no: 50000000,
      ps_co: 0,
      du_no: 50000000,
      du_co: 0,
      ma_ct: 'DC'
    },
    {
      id: '2',
      ma_unit: 'CN',
      ngay_ct: '2024-02-10',
      so_ct: 'DC002',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Đ<PERSON>i chiếu công nợ nhà cung cấp XYZ Solutions',
      ps_no: 75000000,
      ps_co: 0,
      du_no: 75000000,
      du_co: 0,
      ma_ct: 'DC'
    },
    {
      id: '3',
      ma_unit: 'CN',
      ngay_ct: '2024-03-15',
      so_ct: 'DC003',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Đối chiếu công nợ nhà cung cấp DEF Industries',
      ps_no: 30000000,
      ps_co: 0,
      du_no: 30000000,
      du_co: 0,
      ma_ct: 'DC'
    },
    {
      id: '4',
      ma_unit: 'CN',
      ngay_ct: '2024-04-20',
      so_ct: 'DC004',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Đối chiếu công nợ nhà cung cấp GHI Corp',
      ps_no: 0,
      ps_co: 25000000,
      du_no: 0,
      du_co: 25000000,
      ma_ct: 'DC'
    },
    {
      id: '5',
      ma_unit: 'CN',
      ngay_ct: '2024-05-25',
      so_ct: 'DC005',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Đối chiếu công nợ nhà cung cấp JKL Enterprises',
      ps_no: 100000000,
      ps_co: 0,
      du_no: 100000000,
      du_co: 0,
      ma_ct: 'DC'
    },
    {
      id: '6',
      ma_unit: 'CN',
      ngay_ct: '2024-06-12',
      so_ct: 'DC006',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Đối chiếu công nợ nhà cung cấp MNO Trading',
      ps_no: 45000000,
      ps_co: 0,
      du_no: 45000000,
      du_co: 0,
      ma_ct: 'DC'
    },
    {
      id: '7',
      ma_unit: 'CN',
      ngay_ct: '2024-07-08',
      so_ct: 'DC007',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Đối chiếu công nợ nhà cung cấp PQR Logistics',
      ps_no: 0,
      ps_co: 35000000,
      du_no: 0,
      du_co: 35000000,
      ma_ct: 'DC'
    },
    {
      id: '8',
      ma_unit: 'CN',
      ngay_ct: '2024-08-30',
      so_ct: 'DC008',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Đối chiếu công nợ nhà cung cấp STU Manufacturing',
      ps_no: 85000000,
      ps_co: 0,
      du_no: 85000000,
      du_co: 0,
      ma_ct: 'DC'
    },
    {
      id: '9',
      ma_unit: 'CN',
      ngay_ct: '2024-09-14',
      so_ct: 'DC009',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Đối chiếu công nợ nhà cung cấp VWX Services',
      ps_no: 60000000,
      ps_co: 0,
      du_no: 60000000,
      du_co: 0,
      ma_ct: 'DC'
    },
    {
      id: '10',
      ma_unit: 'CN',
      ngay_ct: '2024-10-15',
      so_ct: 'DC010',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Đối chiếu công nợ nhà cung cấp YZ Global',
      ps_no: 0,
      ps_co: 40000000,
      du_no: 0,
      du_co: 40000000,
      ma_ct: 'DC'
    },
    {
      id: '11',
      ma_unit: 'CN',
      ngay_ct: '2024-11-22',
      so_ct: 'DC011',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Đối chiếu công nợ nhà cung cấp Alpha Tech',
      ps_no: 120000000,
      ps_co: 0,
      du_no: 120000000,
      du_co: 0,
      ma_ct: 'DC'
    },
    {
      id: '12',
      ma_unit: 'CN',
      ngay_ct: '2024-12-05',
      so_ct: 'DC012',
      tk: '331',
      tk_du: '131',
      dien_giai: 'Đối chiếu công nợ nhà cung cấp Beta Solutions',
      ps_no: 0,
      ps_co: 55000000,
      du_no: 0,
      du_co: 55000000,
      ma_ct: 'DC'
    }
  ];
};

/**
 * Custom hook for managing SoDoiChieuCongNo (Debt Reconciliation Book) data
 *
 * This hook provides functionality to fetch debt reconciliation book data
 * with mock support for testing and development purposes.
 */
export function useSoDoiChieuCongNo(searchParams: SoDoiChieuCongNoSearchFormValues): UseSoDoiChieuCongNoReturn {
  const [data, setData] = useState<SoDoiChieuCongNoItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SoDoiChieuCongNoSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<SoDoiChieuCongNoResponse>('/ban-hang/cong-no-nha-cung-cap/so-doi-chieu-cong-no/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
