import { useState, useEffect, useCallback } from 'react';
import { NhaCungCap, NhaCungCapInput } from '@/types/schemas/nha-cung-cap.type';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseNhaCungCapReturn {
  nhaCungCaps: NhaCungCap[];
  isLoading: boolean;
  addNhaCungCap: (newNhaCungCap: NhaCungCapInput) => Promise<NhaCungCap>;
  updateNhaCungCap: (uuid: string, updatedNhaCungCap: NhaCungCapInput) => Promise<NhaCungCap>;
  deleteNhaCungCap: (uuid: string) => Promise<void>;
  refreshNhaCungCaps: () => Promise<void>;
  getNhaCungCapById: (uuid: string) => Promise<NhaCungCap | null>;
  searchNhaCungCap: (searchTerm: string) => Promise<NhaCungCap[]>;
  getNhaCungCapByCode: (code: string) => Promise<NhaCungCap | null>;
  getNhaCungCapsByFilter: (filters: Record<string, any>) => Promise<NhaCungCap[]>;
}

/**
 * Hook for managing NhaCungCap (Vendor/Supplier) data
 *
 * This hook provides functions to fetch, create, update, and delete vendors/suppliers.
 * It also includes advanced search and filtering capabilities.
 */
export const useNhaCungCap = (initialNhaCungCaps: NhaCungCap[] = []): UseNhaCungCapReturn => {
  const [nhaCungCaps, setNhaCungCaps] = useState<NhaCungCap[]>(initialNhaCungCaps);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  /**
   * Fetch all vendors/suppliers
   */
  const fetchNhaCungCaps = useCallback(async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get(`/entities/${entity.slug}/erp/vendors/`);
      setNhaCungCaps(response.data.results || []);
    } catch (error) {
      console.error('Error fetching vendors/suppliers:', error);
    } finally {
      setIsLoading(false);
    }
  }, [entity?.slug]);

  /**
   * Get a vendor/supplier by its UUID
   */
  const getNhaCungCapById = async (uuid: string): Promise<NhaCungCap | null> => {
    if (!entity?.slug) return null;

    try {
      const response = await api.get(`/entities/${entity.slug}/erp/vendors/${uuid}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching vendor/supplier with ID ${uuid}:`, error);
      return null;
    }
  };

  /**
   * Get a vendor/supplier by its code
   */
  const getNhaCungCapByCode = async (code: string): Promise<NhaCungCap | null> => {
    if (!entity?.slug || !code) return null;

    try {
      const response = await api.get(`/entities/${entity.slug}/erp/vendors/`, {
        params: { ma_ncc: code }
      });

      const results = response.data.results || [];
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      console.error(`Error fetching vendor/supplier with code ${code}:`, error);
      return null;
    }
  };

  /**
   * Search for vendors/suppliers by a search term
   */
  const searchNhaCungCap = async (searchTerm: string): Promise<NhaCungCap[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get(`/entities/${entity.slug}/erp/vendors/search/`, {
        params: { q: searchTerm }
      });
      return response.data.results || [];
    } catch (error) {
      console.error('Error searching vendors/suppliers:', error);
      return [];
    }
  };

  /**
   * Get vendors/suppliers by filter criteria
   */
  const getNhaCungCapsByFilter = async (filters: Record<string, any>): Promise<NhaCungCap[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get(`/entities/${entity.slug}/erp/vendors/`, {
        params: filters
      });
      return response.data.results || [];
    } catch (error) {
      console.error('Error filtering vendors/suppliers:', error);
      return [];
    }
  };

  /**
   * Add a new vendor/supplier
   */
  const addNhaCungCap = async (newNhaCungCap: NhaCungCapInput): Promise<NhaCungCap> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      // Prepare data before sending to API
      const payload = prepareDataForApi(newNhaCungCap);

      const response = await api.post(`/entities/${entity.slug}/erp/vendors/`, payload);

      const addedNhaCungCap = response.data;
      setNhaCungCaps(prev => [...prev, addedNhaCungCap]);
      return addedNhaCungCap;
    } catch (error) {
      console.error('Error adding vendor/supplier:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Update an existing vendor/supplier
   */
  const updateNhaCungCap = async (uuid: string, updatedNhaCungCap: NhaCungCapInput): Promise<NhaCungCap> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      // Prepare data before sending to API
      const payload = prepareDataForApi(updatedNhaCungCap);

      const response = await api.patch(`/entities/${entity.slug}/erp/vendors/${uuid}/`, payload);

      const updatedItem = response.data;
      setNhaCungCaps(prev => prev.map(item => (item.uuid === uuid ? updatedItem : item)));
      return updatedItem;
    } catch (error) {
      console.error('Error updating vendor/supplier:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Delete a vendor/supplier
   */
  const deleteNhaCungCap = async (uuid: string): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/vendors/${uuid}/`);
      setNhaCungCaps(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting vendor/supplier:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Helper function to prepare data before sending to API
   */
  function prepareDataForApi(data: any): any {
    const payload: any = { ...data };

    // Process foreign keys - convert from object to UUID or code
    const foreignKeyFields = [
      { field: 'ma_nt', objField: 'ma_nt_obj' },
      { field: 'ma_bp', objField: 'ma_bp_obj' },
      { field: 'ma_kv', objField: 'ma_kv_obj' },
      { field: 'ma_nvbh', objField: 'ma_nvbh_obj' },
      { field: 'ma_thue', objField: 'ma_thue_obj' },
      { field: 'tk_no', objField: 'tk_no_obj' },
      { field: 'tk_co', objField: 'tk_co_obj' }
    ];

    foreignKeyFields.forEach(({ field, objField }) => {
      if (payload[objField]) {
        payload[field] = payload[objField].uuid;
        delete payload[objField];
      }
    });

    return payload;
  }

  // Fetch data on component mount or when entity changes
  useEffect(() => {
    fetchNhaCungCaps();
  }, [entity?.slug, fetchNhaCungCaps]);

  return {
    nhaCungCaps,
    isLoading,
    addNhaCungCap,
    updateNhaCungCap,
    deleteNhaCungCap,
    refreshNhaCungCaps: fetchNhaCungCaps,
    getNhaCungCapById,
    searchNhaCungCap,
    getNhaCungCapByCode,
    getNhaCungCapsByFilter
  };
};
