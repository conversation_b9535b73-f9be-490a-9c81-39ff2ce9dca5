import { z } from 'zod';

export const searchSchema = z.object({
  // Basic fields
  ngay_tu: z.string().optional(),
  ngay_den: z.string().optional(),
  tai_khoan: z.string().optional(),
  chi_tiet_theo_vat_tu: z.boolean().optional(),
  tinh_so_du_tung_ngay: z.boolean().optional(),

  // Detail fields
  ma_ncc: z.string().optional(),
  ten_ncc: z.string().optional(),
  mau_bao_cao: z.string().optional(),

  // Other fields
  tu_so_ct: z.string().optional(),
  den_so_ct: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ngay_tu: '',
  ngay_den: '',
  tai_khoan: '',
  chi_tiet_theo_vat_tu: false,
  tinh_so_du_tung_ngay: false,
  ma_ncc: '',
  ten_ncc: '',
  mau_bao_cao: 'tien_chuan',
  tu_so_ct: '',
  den_so_ct: ''
};
