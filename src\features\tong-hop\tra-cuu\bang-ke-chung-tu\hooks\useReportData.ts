import { useState, useCallback, useMemo } from 'react';
import { BangKeChungTuItem, BangKeChungTuSearchFormValues } from '@/types/schemas';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { getDocumentsListColumns } from '../cols-definition';
import { useBangKeChungTu } from './useBangKeChungTu';
import { initialSearchValues } from '../schema';

// Report data hook return type
export interface UseReportDataReturn {
  initialSearchDialogOpen: boolean;
  editPrintTemplateDialogOpen: boolean;
  showTable: boolean;
  searchParams: BangKeChungTuSearchFormValues;
  tables: any[];
  isLoading: boolean;
  error: Error | null;

  // Event handlers
  handleInitialSearchClose: () => void;
  handleInitialSearch: (data: BangKeChungTuSearchFormValues) => void;
  handleSearchClick: () => void;
  handleEditPrintTemplateClick: () => void;
  handleClosePrintTemplateDialog: () => void;
  handleSavePrintTemplate: (template: any) => void;
  handleRowClick: (row: BangKeChungTuItem) => void;
  handleRefreshClick: () => void;
  handleFixedColumnsClick: () => void;
  handleExportDataClick: () => void;
}

/**
 * Main hook for managing BangKeChungTu report data and UI state
 *
 * This hook follows the exact pattern used in tat-toan-hoa-don feature
 * and provides comprehensive state management for the document listing feature.
 */
export function useReportData(): UseReportDataReturn {
  // UI State Management
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [editPrintTemplateDialogOpen, setEditPrintTemplateDialogOpen] = useState(false);
  const [showTable, setShowTable] = useState(false);
  const [searchParams, setSearchParams] = useState<BangKeChungTuSearchFormValues>(initialSearchValues);
  const [selectedRow, setSelectedRow] = useState<BangKeChungTuItem | null>(null);

  // Data Management
  const { data, isLoading, error, fetchData, refreshData } = useBangKeChungTu(searchParams);

  // Calculate totals and add total row
  const dataWithTotals = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Calculate totals
    const totalPsNo = data.reduce((sum, item) => sum + (item.ps_no || 0), 0);
    const totalPsCo = data.reduce((sum, item) => sum + (item.ps_co || 0), 0);

    // Create total row
    const totalRow: BangKeChungTuItem = {
      id: 'total-row',
      unit_id: '',
      ma_unit: '',
      ngay_ct: '//',
      so_ct: '',
      ma_kh: '',
      ten_kh: 'Tổng cộng',
      dien_giai: '',
      tk: '',
      tk_du: '',
      ps_no: totalPsNo,
      ps_co: totalPsCo,
      ma_bp: '',
      ma_vv: '',
      ma_hd: '',
      ma_dtt: '',
      ma_ku: '',
      ma_phi: '',
      ma_sp: '',
      ma_lsx: '',
      ma_cp0: '',
      ma_ct: '',
      isTotal: true
    };

    // Return data with total row at the top
    return [totalRow, ...data];
  }, [data]);

  const tables: TableData[] = [
    {
      name: '',
      rows: dataWithTotals,
      columns: getDocumentsListColumns
    }
  ];

  // Event Handlers
  const handleInitialSearchClose = useCallback(() => {
    setInitialSearchDialogOpen(false);
  }, []);

  const handleInitialSearch = useCallback(
    async (formData: BangKeChungTuSearchFormValues) => {
      setSearchParams(formData);
      setInitialSearchDialogOpen(false);
      setShowTable(true);

      try {
        await fetchData(formData);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    },
    [fetchData]
  );

  const handleSearchClick = useCallback(() => {
    setInitialSearchDialogOpen(true);
  }, []);

  const handleEditPrintTemplateClick = useCallback(() => {
    setEditPrintTemplateDialogOpen(true);
  }, []);

  const handleClosePrintTemplateDialog = useCallback(() => {
    setEditPrintTemplateDialogOpen(false);
  }, []);

  const handleSavePrintTemplate = useCallback((template: any) => {
    console.log('Print template saved:', template);
    setEditPrintTemplateDialogOpen(false);
  }, []);

  const handleRowClick = useCallback((row: BangKeChungTuItem) => {
    setSelectedRow(row);
    console.log('Row clicked:', row);
  }, []);

  const handleRefreshClick = useCallback(async () => {
    try {
      await refreshData();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
  }, [refreshData]);

  const handleFixedColumnsClick = useCallback(() => {
    console.log('Fixed columns clicked');
    // Implement fixed columns functionality
  }, []);

  const handleExportDataClick = useCallback(() => {
    console.log('Export data clicked');
    // Implement export functionality
  }, []);

  return {
    // UI State
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,
    tables,
    isLoading,
    error,

    // Event Handlers
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate,
    handleRowClick,
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick
  };
}
