import { ApiResponse } from '../api.type';

/**
 * TypeScript interface for BaoCaoTinhTrangDeNghiThanhToan (Payment Request Status Report) model
 *
 * This interface represents the structure of the payment request status report data from the backend.
 * It defines the payment request status information used for financial reporting and tracking.
 */
export interface BaoCaoTinhTrangDeNghiThanhToanItem {
  /**
   * Unique identifier
   */
  id: string;

  /**
   * Unit identifier
   */
  unit_id: string;

  /**
   * Document date (YYYY-MM-DD format)
   */
  ngay_ct: string;

  /**
   * Document number
   */
  so_ct: string;

  /**
   * Document type code
   */
  ma_ct: string;

  /**
   * Department code
   */
  ma_bp: string;

  /**
   * User identifier
   */
  user_id: string;

  /**
   * Description/explanation
   */
  dien_giai: string;

  /**
   * Total amount
   */
  tien: number;

  /**
   * Amount paid/spent
   */
  tien_chi: number;

  /**
   * Status of the payment request
   */
  status: string;

  /**
   * Remaining amount
   */
  tien_cl: number;

  /**
   * Department name
   */
  ten_bp: string;

  /**
   * Document status name
   */
  ten_ttct: string;

  /**
   * Unit code
   */
  ma_unit: string;

  /**
   * Requester name
   */
  nguoi_yc: string;
}

/**
 * Type for BaoCaoTinhTrangDeNghiThanhToan API response
 */
export type BaoCaoTinhTrangDeNghiThanhToanResponse = ApiResponse<BaoCaoTinhTrangDeNghiThanhToanItem>;

/**
 * Search form values interface for payment request status report
 */
export interface BaoCaoTinhTrangDeNghiThanhToanSearchFormValues {
  ngay_ct1: Date;
  ngay_ct2: Date;
  so_ct1?: string;
  so_ct2?: string;
  status: string;
  mau_bc: string;
  dien_giai?: string;
  report_filtering: string;
  [key: string]: any;
}

/**
 * Return type for useBaoCaoTinhTrangDeNghiThanhToan hook
 */
export interface UseBaoCaoTinhTrangDeNghiThanhToanReturn {
  data: BaoCaoTinhTrangDeNghiThanhToanItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: BaoCaoTinhTrangDeNghiThanhToanSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
