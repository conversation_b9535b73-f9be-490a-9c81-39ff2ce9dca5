import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { BangTinhPhanBoCCDCTTItem } from '@/types/schemas';

export interface UseTableDataReturn {
  tables: TableData[];
}

export function useTableData(data: BangTinhPhanBoCCDCTTItem[], searchParams?: any): UseTableDataReturn {
  const tables = useMemo(() => {
    const dynamicMonthColumn =
      searchParams?.den_ky && searchParams?.den_nam
        ? `${String(searchParams.den_ky).padStart(2, '0')}${searchParams.den_nam}${String(searchParams.den_ky).padStart(2, '0')}`
        : '04202505';

    const calculateTotalRow = (): BangTinhPhanBoCCDCTTItem => {
      const totals = data.reduce(
        (acc, item) => {
          acc.so_luong += item.so_luong || 0;
          acc.systotal += item.systotal || 0;
          acc['01'] += item['01'] || 0;
          acc['02'] += item['02'] || 0;
          acc['03'] += item['03'] || 0;
          acc[dynamicMonthColumn] += item[dynamicMonthColumn] || 0;
          acc['05'] += item['05'] || 0;
          acc['06'] += item['06'] || 0;
          acc['07'] += item['07'] || 0;
          return acc;
        },
        {
          so_luong: 0,
          systotal: 0,
          '01': 0,
          '02': 0,
          '03': 0,
          [dynamicMonthColumn]: 0,
          '05': 0,
          '06': 0,
          '07': 0
        }
      );

      return {
        id: 'total',
        ma_cc: '',
        ten_cc: 'Tổng cộng',
        ma_lcc: '',
        ten_lcc: '',
        ten_dvt: '',
        so_luong: totals.so_luong,
        ngay_kh0: '',
        so_ky_kh: 0,
        ngay_kh_kt: '',
        tk_cc: '',
        ma_bp: '',
        systotal: totals.systotal,
        '01': totals['01'],
        '02': totals['02'],
        '03': totals['03'],
        [dynamicMonthColumn]: totals[dynamicMonthColumn],
        '05': totals['05'],
        '06': totals['06'],
        '07': totals['07'],
        isTotal: true
      };
    };

    const totalRow = calculateTotalRow();
    const tableRows = [totalRow, ...data];

    const tableData: TableData[] = [
      {
        name: '',
        columns: [
          {
            field: 'ma_cc',
            headerName: 'Mã công cụ',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ma_cc || '';
            }
          },
          {
            field: 'ten_cc',
            headerName: 'Tên công cụ',
            width: 250,
            renderCell: (params: any) => {
              if (params.row.isTotal) {
                return params.row.ten_cc;
              }
              return params.row.ten_cc;
            }
          },
          {
            field: 'ma_lcc',
            headerName: 'Mã loại CC',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ma_lcc || '';
            }
          },
          {
            field: 'ten_lcc',
            headerName: 'Tên loại công cụ',
            width: 150,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ten_lcc || '';
            }
          },
          {
            field: 'ten_dvt',
            headerName: 'Đơn vị tính',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ten_dvt || '';
            }
          },
          {
            field: 'so_luong',
            headerName: 'Số lượng',
            width: 100,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.so_luong || 0;
              const formattedValue = value.toLocaleString('vi-VN');
              return params.row.isTotal ? `${formattedValue}` : formattedValue;
            }
          },
          {
            field: 'ngay_kh0',
            headerName: 'Ngày KH ban đầu',
            width: 140,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ngay_kh0 || '';
            }
          },
          {
            field: 'so_ky_kh',
            headerName: 'Số kỳ KH',
            width: 100,
            type: 'number',
            renderCell: (params: any) => {
              if (params.row.isTotal) return '';
              return params.row.so_ky_kh || 0;
            }
          },
          {
            field: 'ngay_kh_kt',
            headerName: 'Ngày KH kết thúc',
            width: 140,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ngay_kh_kt || '';
            }
          },
          {
            field: 'tk_cc',
            headerName: 'TK công cụ',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.tk_cc || '';
            }
          },
          {
            field: 'ma_bp',
            headerName: 'Mã bộ phận',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ma_bp || '';
            }
          },
          {
            field: 'systotal',
            headerName: 'Tiền',
            width: 140,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.systotal || 0;
              const formattedValue = value.toLocaleString('vi-VN');
              return params.row.isTotal ? `${formattedValue}` : formattedValue;
            }
          },
          {
            field: '01',
            headerName: 'Nguyên giá',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row['01'] || 0;
              const formattedValue = value.toLocaleString('vi-VN');
              return params.row.isTotal ? `${formattedValue}` : formattedValue;
            }
          },
          {
            field: '02',
            headerName: 'Gt đã pb đầu kỳ',
            width: 140,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row['02'] || 0;
              const formattedValue = value.toLocaleString('vi-VN');
              return params.row.isTotal ? `${formattedValue}` : formattedValue;
            }
          },
          {
            field: '03',
            headerName: 'Gt còn lại đầu kỳ',
            width: 140,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row['03'] || 0;
              const formattedValue = value.toLocaleString('vi-VN');
              return params.row.isTotal ? `${formattedValue}` : formattedValue;
            }
          },
          {
            field: dynamicMonthColumn,
            headerName:
              searchParams?.den_ky && searchParams?.den_nam
                ? `${String(searchParams.den_ky).padStart(2, '0')}/${searchParams.den_nam}`
                : '05/2025',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row[dynamicMonthColumn] || 0;
              const formattedValue = value.toLocaleString('vi-VN');
              return params.row.isTotal ? `${formattedValue}` : formattedValue;
            }
          },
          {
            field: '05',
            headerName: 'Tổng phân bổ',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row['05'] || 0;
              const formattedValue = value.toLocaleString('vi-VN');
              return params.row.isTotal ? `${formattedValue}` : formattedValue;
            }
          },
          {
            field: '06',
            headerName: 'Gt pb luy kế',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row['06'] || 0;
              const formattedValue = value.toLocaleString('vi-VN');
              return params.row.isTotal ? `${formattedValue}` : formattedValue;
            }
          },
          {
            field: '07',
            headerName: 'Gt còn lại cuối kỳ',
            width: 140,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row['07'] || 0;
              const formattedValue = value.toLocaleString('vi-VN');
              return params.row.isTotal ? `${formattedValue}` : formattedValue;
            }
          }
        ],
        rows: tableRows
      }
    ];

    return tableData;
  }, [data, searchParams]);

  return {
    tables
  };
}
