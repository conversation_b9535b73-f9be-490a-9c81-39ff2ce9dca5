import { useState, useCallback } from 'react';
import {
  BangKeBanRaItem,
  BangKeBanRaResponse,
  ToKhaiThueGTGTSearchFormValues,
  UseBangKeBanRaReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): BangKeBanRaItem[] => {
  const customers = [
    'Công ty TNHH ABC Technology',
    'Công ty CP XYZ Solutions',
    'Doanh nghiệp tư nhân DEF',
    'Công ty TNHH GHI Trading',
    'Tập đoàn JKL Group'
  ];

  const products = [
    'Phần mềm quản lý',
    'Dịch vụ tư vấn IT',
    '<PERSON>hi<PERSON>t bị máy tính',
    'Dịch vụ bảo trì',
    'Giải pháp công nghệ'
  ];

  return Array.from({ length: 15 }, (_, index) => {
    const currentDate = new Date();
    const randomDays = Math.floor(Math.random() * 30);
    const invoiceDate = new Date(currentDate.getTime() - randomDays * 24 * 60 * 60 * 1000);

    return {
      id: (index + 1).toString(),
      so_hoa_don: `HD${String(index + 1).padStart(6, '0')}`,
      ngay_hoa_don: invoiceDate, // Keep as Date object for MUI DataGrid
      ky_hieu: `AA/24E`,
      ten_khach_hang: customers[index % customers.length],
      ten_mat_hang: products[index % products.length],
      ma_so_thue: `${1000000000 + index * 111111}`,
      tien_ban: Math.floor(Math.random() * 100000000) + 10000000,
      thue_suat: [0, 5, 10][Math.floor(Math.random() * 3)],
      tien_thue: Math.floor(Math.random() * 10000000) + 1000000,
      ghi_chu: `Ghi chú hóa đơn ${index + 1}`
    };
  });
};

export function useBangKeBanRa(): UseBangKeBanRaReturn {
  const [data, setData] = useState<BangKeBanRaItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: ToKhaiThueGTGTSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BangKeBanRaResponse>('/thue/to-khai/to-khai-thue-gtgt/bang-ke-ban-ra/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData({});
  }, [fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
