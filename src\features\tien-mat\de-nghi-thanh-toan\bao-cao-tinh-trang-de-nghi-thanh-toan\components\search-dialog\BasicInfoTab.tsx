'use client';

import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
}

export const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày từ/đến</Label>
          <AritoFormDateRangeDropdown fromDateName='ngay_ct1' toDateName='ngay_ct2' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số c/từ(từ/đến)</Label>
          <FormField type='text' name='so_ct1' disabled={formMode === 'view'} placeholder='Từ số' />
          <FormField type='text' name='so_ct2' disabled={formMode === 'view'} className='ml-2' placeholder='Đến số' />
        </div>
      </div>
    </div>
  );
};
