import { useState, useCallback } from 'react';
import { BangTinhKhauHaoTSCDItem, BangTinhKhauHaoTSCDResponse, UseBangTinhKhauHaoTSCDReturn } from '@/types/schemas';
import { SearchFormValues } from '../schema';
import api from '@/lib/api';

export type BangTinhKhauHaoTSCDSearchFormValues = SearchFormValues;

const generateMockData = (): BangTinhKhauHaoTSCDItem[] => {
  return [
    {
      id: '1',
      stt: 1,
      ma_ts: 'TS001',
      ten_ts: '<PERSON><PERSON><PERSON> tính xách tay Dell Inspiron 15',
      ten_dvt: 'Cái',
      so_luong: 1,
      ma_lts: 'LTS001',
      ten_lts: 'Thiết bị văn phòng',
      ngay_kh0: '2025-01-15',
      so_ky_kh: 36,
      ngay_kh_kt: '2028-01-15',
      nguyen_gia_nt: 1000,
      gt_da_kh_dk_nt: 100,
      gt_cl_dk_nt: 900,
      gt_da_kh_nt: 27.78,
      gt_da_kh_lk_nt: 127.78,
      gt_cl_ck_nt: 872.22,
      ma_nt: 'USD',
      ty_gia: 25000,
      nguyen_gia: 25000000,
      gt_da_kh_dk: 2500000,
      gt_cl_dk: 22500000,
      gt_da_kh: 694444,
      gt_da_kh_lk: 3194444,
      gt_cl_ck: 21805556,
      tk_ts: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'IT',
      ma_vv: 'VV001',
      ma_phi: 'PHI001',
      systotal: 0
    },
    {
      id: '2',
      stt: 2,
      ma_ts: 'TS002',
      ten_ts: 'Màn hình LCD Samsung 27 inch',
      ten_dvt: 'Cái',
      so_luong: 1,
      ma_lts: 'LTS001',
      ten_lts: 'Thiết bị văn phòng',
      ngay_kh0: '2025-01-16',
      so_ky_kh: 24,
      ngay_kh_kt: '2027-01-16',
      nguyen_gia_nt: 340,
      gt_da_kh_dk_nt: 34,
      gt_cl_dk_nt: 306,
      gt_da_kh_nt: 14.17,
      gt_da_kh_lk_nt: 48.17,
      gt_cl_ck_nt: 291.83,
      ma_nt: 'USD',
      ty_gia: 25000,
      nguyen_gia: 8500000,
      gt_da_kh_dk: 850000,
      gt_cl_dk: 7650000,
      gt_da_kh: 354167,
      gt_da_kh_lk: 1204167,
      gt_cl_ck: 7295833,
      tk_ts: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'IT',
      ma_vv: 'VV001',
      ma_phi: 'PHI001',
      systotal: 0
    },
    {
      id: '3',
      stt: 3,
      ma_ts: 'TS003',
      ten_ts: 'Server Dell PowerEdge R740',
      ten_dvt: 'Bộ',
      so_luong: 1,
      ma_lts: 'LTS002',
      ten_lts: 'Máy móc thiết bị',
      ngay_kh0: '2025-01-20',
      so_ky_kh: 60,
      ngay_kh_kt: '2030-01-20',
      nguyen_gia_nt: 6000,
      gt_da_kh_dk_nt: 200,
      gt_cl_dk_nt: 5800,
      gt_da_kh_nt: 100,
      gt_da_kh_lk_nt: 300,
      gt_cl_ck_nt: 5700,
      ma_nt: 'USD',
      ty_gia: 25000,
      nguyen_gia: 150000000,
      gt_da_kh_dk: 5000000,
      gt_cl_dk: 145000000,
      gt_da_kh: 2500000,
      gt_da_kh_lk: 7500000,
      gt_cl_ck: 142500000,
      tk_ts: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'IT',
      ma_vv: 'VV002',
      ma_phi: 'PHI002',
      systotal: 0
    },
    {
      id: '4',
      stt: 4,
      ma_ts: 'TS004',
      ten_ts: 'Bàn làm việc gỗ cao cấp',
      ten_dvt: 'Cái',
      so_luong: 5,
      ma_lts: 'LTS003',
      ten_lts: 'Nội thất văn phòng',
      ngay_kh0: '2025-02-01',
      so_ky_kh: 120,
      ngay_kh_kt: '2035-02-01',
      nguyen_gia_nt: 0,
      gt_da_kh_dk_nt: 0,
      gt_cl_dk_nt: 0,
      gt_da_kh_nt: 0,
      gt_da_kh_lk_nt: 0,
      gt_cl_ck_nt: 0,
      ma_nt: 'VND',
      ty_gia: 1,
      nguyen_gia: 12000000,
      gt_da_kh_dk: 200000,
      gt_cl_dk: 11800000,
      gt_da_kh: 100000,
      gt_da_kh_lk: 300000,
      gt_cl_ck: 11700000,
      tk_ts: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'HC',
      ma_vv: 'VV003',
      ma_phi: 'PHI003',
      systotal: 0
    }
  ];
};

/**
 * Custom hook for managing BangTinhKhauHaoTSCD (Fixed Asset Depreciation Calculation Table) data
 *
 * This hook provides functionality to fetch fixed asset depreciation calculation table data
 * with mock support for testing and development purposes.
 */
export function useBangTinhKhauHaoTSCD(
  searchParams: BangTinhKhauHaoTSCDSearchFormValues
): UseBangTinhKhauHaoTSCDReturn {
  const [data, setData] = useState<BangTinhKhauHaoTSCDItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BangTinhKhauHaoTSCDSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BangTinhKhauHaoTSCDResponse>('/tai-san/tang-giam-tscd/bang-tinh-khau-hao-tscd/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
