'use client';

import { useState } from 'react';
import { AritoDialog, AritoForm, BottomBar, AritoIcon } from '@/components/custom/arito';
import { SearchFormValues, searchFormSchema } from '../../schema';
import { GeneralInfoTab } from './GeneralInfoTab';
import { BasicInfoTab } from './BasicInfoTab';
import { BoPhan } from '@/types/schemas';
import { FormMode } from '@/types/form';
import { OtherTab } from './OtherTab';

interface SearchDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: SearchFormValues;
  onSubmit?: (data: SearchFormValues) => void;
  onClose: () => void;
}

const SearchDialog = ({ open, onClose, onSubmit, formMode, initialData }: SearchDialogProps) => {
  const [boPhan, setBoPhan] = useState<BoPhan | null>(null);

  const handleSubmit = (data: SearchFormValues) => {
    onSubmit?.(data);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={'Báo cáo tình trạng đề nghị chi'}
      maxWidth='xl'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode={formMode}
        hasAritoActionBar={false}
        schema={searchFormSchema}
        onSubmit={handleSubmit}
        initialData={initialData}
        className='w-[900px]'
        headerFields={<BasicInfoTab formMode={formMode} />}
        tabs={[
          {
            id: 'general_info',
            label: 'Thông tin chung',
            component: <GeneralInfoTab formMode={formMode} boPhan={boPhan} setBoPhan={setBoPhan} />
          },
          {
            id: 'other',
            label: 'Khác',
            component: <OtherTab formMode={formMode} />
          }
        ]}
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={<BottomBar mode={formMode} onClose={onClose} />}
      />
    </AritoDialog>
  );
};

export default SearchDialog;
