import { useState, useEffect, useCallback } from 'react';
import { GridRowParams } from '@mui/x-data-grid';
import { soChiTietCongNoTieuChuanColumns, soChiTietCongNoNgoaiTeColumns } from '../cols-definition';
import useVendorDebtDetailData from './useVendorDebtDetailData';
import { SearchFormValues } from '../schemas';

export interface UseTableDataReturn {
  tables: Array<{
    name: string;
    rows: any[];
    columns: any[];
  }>;
  selectedRowIndex: string | null;
  handleRowClick: (params: GridRowParams) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
}

const useTableData = (searchParams: SearchFormValues | null): UseTableDataReturn => {
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const { data, isLoading, error, fetchData, refreshData } = useVendorDebtDetailData();

  // Fetch data when searchParams change
  useEffect(() => {
    if (searchParams) {
      fetchData(searchParams);
    }
  }, [searchParams, fetchData]);

  const handleRowClick = useCallback((params: GridRowParams) => {
    setSelectedRowIndex(params.row.id.toString());
  }, []);

  // Determine which columns to use based on report template
  const getColumns = () => {
    const reportTemplate = searchParams?.mau_bao_cao || 'tien_chuan';
    return reportTemplate === 'tien_chuan'
      ? soChiTietCongNoTieuChuanColumns(
          () => {},
          () => {}
        )
      : soChiTietCongNoNgoaiTeColumns(
          () => {},
          () => {}
        );
  };

  const tables = [
    {
      name: '',
      rows: data.map(row => ({
        ...row,
        id: row.id
      })),
      columns: getColumns()
    }
  ];

  return {
    tables,
    selectedRowIndex,
    handleRowClick,
    isLoading,
    error,
    refreshData
  };
};

export default useTableData;
