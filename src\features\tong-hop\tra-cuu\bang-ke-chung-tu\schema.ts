import { z } from 'zod';

export const SearchFormSchema = z.object({
  ngay_ct1: z.coerce.date(),
  ngay_ct2: z.coerce.date(),
  so_ct1: z.string().optional(),
  so_ct2: z.string().optional(),

  tk: z.string().optional(),
  tk_du: z.string().optional(),
  no_co: z.string().optional(),
  ma_kh: z.string().optional(),
  ma_nt: z.string().optional(),
  ma_ct: z.string().optional(),
  dien_giai: z.string().optional(),
  mau_bc: z.string().optional(),

  report_filtering: z.string().optional(),
  data_analysis_struct: z.string().optional(),

  ma_bp: z.string().optional(),
  ma_vv: z.string().optional(),
  ma_hd: z.string().optional(),
  ma_dtt: z.string().optional(),

  ma_ku: z.string().optional(),
  ma_phi: z.string().optional(),
  ma_sp: z.string().optional(),
  ma_lsx: z.string().optional()
});

export type SearchFormValues = z.infer<typeof SearchFormSchema>;

export const initialSearchValues: SearchFormValues = {
  ngay_ct1: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
  ngay_ct2: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
  so_ct1: '',
  so_ct2: '',

  no_co: 'all',
  mau_bc: '20',
  report_filtering: '0',
  data_analysis_struct: '0',

  tk: '',
  ma_ct: '',
  ma_kh: '',
  tk_du: '',
  dien_giai: '',
  ma_bp: '',
  ma_vv: '',
  ma_hd: '',
  ma_ku: '',
  ma_phi: '',
  ma_sp: '',
  ma_lsx: '',
  ma_dtt: '',
  ma_nt: ''
};
