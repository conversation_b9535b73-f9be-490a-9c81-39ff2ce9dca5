import { GridColDef } from '@mui/x-data-grid';

export const purchaseReportGroupColumns = (handleOpenViewForm: any, handleOpenEditForm: any): GridColDef[] => [
  { field: 'ma_vt', headerName: 'Mã vật tư', width: 120 },
  { field: 'ten', headerName: 'Tên vật tư', width: 200 },
  { field: 'sl_nhap', headerName: 'Số lượng', width: 100, type: 'number' },
  { field: 'gia', headerName: 'Giá', width: 120, type: 'number' },
  { field: 'tien_nhap', headerName: 'Tiền', width: 150, type: 'number' },
  { field: 'thue', headerName: 'Thuế', width: 120, type: 'number' },
  { field: 'cp', headerName: 'Chi phí', width: 120, type: 'number' }
];

// Keep the original export for backward compatibility
export const exportMainColumns = purchaseReportGroupColumns;

export const exportPrintColColums: GridColDef[] = [
  { field: 'col', headerName: 'Cột', width: 150 },
  { field: 'colName', headerName: 'Tên cột', width: 150 },
  { field: 'colNameEng', headerName: 'Tên tiếng anh', width: 150 },
  { field: 'colWidth', headerName: 'Độ rộng', width: 150 },
  { field: 'colType', headerName: 'Định dạng', width: 150 },
  { field: 'colAlign', headerName: 'Căn chỉnh', width: 150 },
  { field: 'colBold', headerName: 'In đậm', width: 150 },
  { field: 'colItalic', headerName: 'In nghiêng', width: 150 },
  { field: 'colSum', headerName: 'Tổng', width: 150 }
];

export const KhuVucColDef: GridColDef[] = [
  { field: 'ma_khu_vuc', headerName: 'Mã khu vực', flex: 1 },
  { field: 'ten_khu_vuc', headerName: 'Tên khu vực', flex: 2 }
];

export const GiaoDichColDef: GridColDef[] = [
  { field: 'checkbox', headerName: '', width: 50 },
  { field: 'ma_giao_dich', headerName: 'Mã giao dịch', flex: 1 },
  { field: 'ten_giao_dich', headerName: 'Tên giao dịch', flex: 2 },
  { field: 'ma_chung_tu', headerName: 'Mã chứng từ', flex: 1 }
];

export const MaLoColDef: GridColDef[] = [
  { field: 'ma_lo', headerName: 'Mã lô', flex: 1 },
  { field: 'ten_lo', headerName: 'Tên lô', flex: 2 }
];

export const MaNCCSearchCol: GridColDef[] = [
  { field: 'code', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'supplier_name', headerName: 'Tên đối tượng', flex: 2 },
  { field: 'receivable', headerName: 'Công nợ p/thu', flex: 1 },
  { field: 'payable', headerName: 'Công nợ p/trả', flex: 1 },
  { field: 'tax_code', headerName: 'Mã số thuế', flex: 1 },
  { field: 'email', headerName: 'Email', flex: 1 },
  { field: 'phone', headerName: 'Số điện thoại', flex: 1 }
];

export const MaVatTuSearchCol: GridColDef[] = [
  { field: 'ma_vat_tu', headerName: 'Mã vật tư', width: 150 },
  { field: 'ten_vat_tu', headerName: 'Tên vật tư', width: 250 },
  { field: 'dvt', headerName: 'Đvt', width: 80 },
  { field: 'nhom_1', headerName: 'Nhóm 1', width: 120 },
  { field: 'theo_doi_lo', headerName: 'Theo dõi lô', width: 120 },
  { field: 'quy_cach', headerName: 'Quy cách', width: 150 },
  { field: 'hinh_anh', headerName: 'Hình ảnh', width: 100 }
];

export const MaViTriColDef: GridColDef[] = [
  { field: 'ma_vi_tri', headerName: 'Mã vị trí', flex: 1 },
  { field: 'ten_vi_tri', headerName: 'Tên vị trí', flex: 2 }
];
