import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS, taiSanSearchColumns } from '@/constants';
import type { TaiSanCoDinh } from '@/types/schemas';
import { Label } from '@/components/ui/label';

interface BasicInfoProps {
  taiSan?: TaiSanCoDinh | null;
  setTaiSan?: (taiSan: TaiSanCoDinh) => void;
}

const BasicInfo: React.FC<BasicInfoProps> = ({ taiSan, setTaiSan }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Từ kỳ/năm</Label>
          <div className='flex items-center gap-2'>
            <FormField name='tu_ky' type='text' className='w-20' />
            <FormField name='tu_nam' type='text' className='w-24' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Đến kỳ/năm</Label>
          <div className='flex items-center gap-2'>
            <FormField name='den_ky' type='text' className='w-20' />
            <FormField name='den_nam' type='text' className='w-24' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Tài sản</Label>
          <SearchField<TaiSanCoDinh>
            searchEndpoint={`/${QUERY_KEYS.KHAI_BAO_THONG_TIN_TSCD}/`}
            searchColumns={taiSanSearchColumns}
            dialogTitle='Danh mục tài sản'
            columnDisplay='ma_ts'
            displayRelatedField='ten_ts'
            value={taiSan?.ma_ts || ''}
            onRowSelection={setTaiSan}
          />
        </div>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Lọc tài sản</Label>
          <FormField
            name='xu_ly'
            type='select'
            className='min-w-40'
            options={[
              { label: 'Chỉ lấy tài sản còn khấu hao', value: '1' },
              { label: 'Lấy tài sản còn khấu hao hoặc đã hết khấu hao nhưng chưa khai báo giảm', value: '2' }
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
