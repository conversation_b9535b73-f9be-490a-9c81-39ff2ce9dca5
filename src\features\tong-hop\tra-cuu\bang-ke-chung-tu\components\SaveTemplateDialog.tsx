import { Button } from '@mui/material';
import React from 'react';
import { z } from 'zod';
import AritoCreateAnalysisSample from '@/components/custom/arito/create-analysis-sample';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { Label } from '@/components/ui/label';

interface SaveTemplateDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: SaveTemplateFormData) => void;
  templateType: 'filter' | 'analysis';
}

export interface SaveTemplateFormData {
  templateName: string;
  templateName2?: string;
  analysisTemplate?: string;
}

const saveTemplateSchema = z.object({
  templateName: z.string().min(1, 'Tên mẫu không được để trống'),
  templateName2: z.string().optional(),
  analysisTemplate: z.string().optional()
});

const SaveTemplateDialog: React.FC<SaveTemplateDialogProps> = ({ open, onClose, onSave, templateType }) => {
  const title = templateType === 'filter' ? 'Lưu mẫu báo cáo' : 'Lưu mới mẫu phân tích';

  const actions = (
    <>
      <Button
        variant='contained'
        color='primary'
        type='button'
        onClick={() => {
          const form = document.querySelector('form');
          if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
        }}
        className='bg-blue-600 normal-case hover:bg-blue-800'
      >
        <span className='mx-1'>
          <AritoIcon icon={884} />
        </span>
        Đồng ý
      </Button>

      <Button
        variant='outlined'
        onClick={onClose}
        className='border-blue-600 normal-case text-blue-600 hover:border-blue-800 hover:bg-blue-50'
      >
        <span className='mx-1'>
          <AritoIcon icon={885} />
        </span>
        Hủy
      </Button>
    </>
  );

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      titleIcon={<AritoIcon icon={12} />}
      maxWidth='lg'
      actions={actions}
    >
      <div className='max-h-[80vh] w-[800px] min-w-[800px] overflow-y-auto bg-gray-50'>
        <AritoForm
          mode='add'
          initialData={{
            templateName: '',
            templateName2: '',
            analysisTemplate: ''
          }}
          onSubmit={onSave}
          schema={saveTemplateSchema}
          hasAritoActionBar={false}
          tabs={
            <div className='w-full space-y-2'>
              <div className='grid w-full grid-cols-1'>
                <div className='rounded-md bg-white p-2'>
                  <div className='flex items-center'>
                    <Label className='w-40 min-w-40 font-medium text-gray-700'>
                      {templateType === 'filter' ? 'Nhập tên mẫu mới:' : 'Nhập tên mới mẫu:'}
                    </Label>
                    <div className='flex-1'>
                      <FormField name='templateName' label='' type='text' className='w-full' />
                    </div>
                  </div>

                  <div className='flex items-center'>
                    <Label className='w-40 min-w-40 font-medium text-gray-700'>Tên 2:</Label>
                    <div className='flex-1'>
                      <FormField name='templateName2' label='' type='text' className='w-full' />
                    </div>
                  </div>
                </div>

                {templateType === 'analysis' && (
                  <div className='mb-4 border-b border-gray-200'>
                    <AritoHeaderTabs
                      tabs={[
                        {
                          id: 'info',
                          label: 'Mẫu phân tích',
                          component: (
                            <div className='rounded-md bg-white p-2'>
                              <AritoCreateAnalysisSample
                                columnAnalysisItems={[
                                  { label: 'Nhóm', type: 'array' },
                                  { label: 'Mã %s', type: 'array' },
                                  { label: 'Tên %s', type: 'array' },
                                  { label: 'Số lượng', type: 'number' },
                                  { label: 'Giá bản nt', type: 'number' },
                                  { label: 'Doanh số nt', type: 'number' },
                                  { label: 'Thuế nt', type: 'number' },
                                  { label: 'Chiết khấu nt', type: 'number' },
                                  { label: 'Trả lại nt', type: 'number' },
                                  { label: 'Phải thu nt', type: 'number' },
                                  { label: 'Doanh thu nt', type: 'number' },
                                  { label: 'Giá vốn nt', type: 'number' },
                                  { label: 'Tiền xuất nt', type: 'number' },
                                  { label: 'Tiền vốn nt', type: 'number' },
                                  { label: 'Lãi nt', type: 'number' },
                                  { label: 'Giá bán', type: 'number' },
                                  { label: 'Doanh số', type: 'number' },
                                  { label: 'Thuế', type: 'number' },
                                  { label: 'Chiết khấu', type: 'number' },
                                  { label: 'Trả lại', type: 'number' },
                                  { label: 'Phải thu', type: 'number' },
                                  { label: 'Doanh thu', type: 'number' },
                                  { label: 'Giá vốn', type: 'number' },
                                  { label: 'Tiền vốn', type: 'number' },
                                  { label: 'Lãi', type: 'number' },
                                  { label: '% GV/GB nt', type: 'number' },
                                  { label: '% GV/GB', type: 'number' }
                                ]}
                              />
                            </div>
                          )
                        }
                      ]}
                    />
                  </div>
                )}
              </div>
            </div>
          }
        />
      </div>
    </AritoDialog>
  );
};

export default SaveTemplateDialog;
