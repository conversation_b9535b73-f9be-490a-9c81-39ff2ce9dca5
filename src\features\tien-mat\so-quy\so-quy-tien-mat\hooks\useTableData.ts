import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import { cashBookColumns } from '../cols-definition';
import { useCashBookData } from './useCashBookData';
import { SearchFormValues } from '../schemas';

export interface UseTableDataReturn {
  tables: Array<{
    name: string;
    rows: any[];
    columns: any[];
  }>;
  handleRowClick: (params: GridRowParams) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
}

export function useTableData(searchParams: SearchFormValues = {}): UseTableDataReturn {
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const { data, isLoading, error, refreshData } = useCashBookData(searchParams);

  const handleRowClick = (params: GridRowParams) => {
    const cashBookItem = params.row as any;
    setSelectedRowIndex(params.row.id.toString());
    console.log('Row clicked:', cashBookItem);
  };

  const tables = [
    {
      name: '',
      rows: data,
      columns: cashBookColumns
    }
  ];

  return {
    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData
  };
}

export default useTableData;
