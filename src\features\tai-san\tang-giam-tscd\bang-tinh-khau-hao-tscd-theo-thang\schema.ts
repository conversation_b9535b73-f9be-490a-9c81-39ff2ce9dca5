import { z } from 'zod';

export const searchSchema = z.object({
  // Basic Info fields
  tu_ky: z.number().optional(), // From period
  tu_nam: z.number().optional(), // From year
  den_ky: z.number().optional(), // To period
  den_nam: z.number().optional(), // To year

  // SearchField fields
  ma_ts: z.string().optional(), // Asset code
  ma_lts: z.string().optional(), // Asset type code
  ma_bp: z.string().optional(), // Department code
  nh_ts1: z.string().optional(), // Asset group 1
  nh_ts2: z.string().optional(), // Asset group 2
  nh_ts3: z.string().optional(), // Asset group 3

  // System fields
  xu_ly: z.string().optional(), // Processing flag
  ma_unit: z.string().optional(), // Unit code
  mau_bc: z.number().optional(), // Report template
  data_analysis_struct: z.string().optional() // Data analysis structure
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  // Basic Info fields
  tu_ky: 5,
  tu_nam: 2025,
  den_ky: 5,
  den_nam: 2025,

  // SearchField fields
  ma_ts: '',
  ma_lts: '',
  ma_bp: '',
  nh_ts1: '',
  nh_ts2: '',
  nh_ts3: '',

  // System fields
  xu_ly: '1',
  ma_unit: '',
  mau_bc: 20,
  data_analysis_struct: '1'
};
