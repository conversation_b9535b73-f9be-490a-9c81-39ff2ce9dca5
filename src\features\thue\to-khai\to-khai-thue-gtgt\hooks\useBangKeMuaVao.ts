import { useState, useCallback } from 'react';
import {
  BangKeMuaVaoItem,
  BangKeMuaVaoResponse,
  ToKhaiThueGTGTSearchFormValues,
  UseBangKeMuaVaoReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): BangKeMuaVaoItem[] => {
  const suppliers = [
    'Công ty TNHH Phần mềm Việt',
    'Công ty CP Công nghệ ABC',
    'Doanh nghiệp XYZ Solutions',
    'Công ty TNHH DEF Trading',
    'Tập đoàn GHI Technology'
  ];

  const materials = ['Thiết bị máy tính', 'Phần mềm bản quyền', 'Dịch vụ internet', 'Thiết bị mạng', 'Dịch vụ hosting'];

  return Array.from({ length: 12 }, (_, index) => {
    const currentDate = new Date();
    const randomDays = Math.floor(Math.random() * 30);
    const invoiceDate = new Date(currentDate.getTime() - randomDays * 24 * 60 * 60 * 1000);
    const tienChuaThue = Math.floor(Math.random() * 50000000) + 10000000;
    const thueSuat = [0, 5, 10][Math.floor(Math.random() * 3)];
    const tienThue = (tienChuaThue * thueSuat) / 100;

    return {
      id: (index + 1).toString(),
      so_hoa_don: `MV${String(index + 1).padStart(6, '0')}`,
      ngay_hoa_don: invoiceDate, // Keep as Date object for MUI DataGrid
      so_chung_tu: `CT${String(index + 1).padStart(6, '0')}`,
      ky_hieu: `BB/24E`,
      ten_khach_hang: suppliers[index % suppliers.length],
      ma_so_thue: `${2000000000 + index * 222222}`,
      ten_mat_hang: materials[index % materials.length],
      tien_chua_thue: tienChuaThue,
      thue_suat: thueSuat,
      tien_thue: tienThue,
      tong_tien: tienChuaThue + tienThue,
      ghi_chu: `Ghi chú mua vào ${index + 1}`
    };
  });
};

export function useBangKeMuaVao(): UseBangKeMuaVaoReturn {
  const [data, setData] = useState<BangKeMuaVaoItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: ToKhaiThueGTGTSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BangKeMuaVaoResponse>('/thue/to-khai/to-khai-thue-gtgt/bang-ke-mua-vao/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData({});
  }, [fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
