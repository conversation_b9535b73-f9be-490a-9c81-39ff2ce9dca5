import { useState, useCallback } from 'react';
import {
  BangKeChungTuItem,
  BangKeChungTuResponse,
  BangKeChungTuSearchFormValues,
  UseBangKeChungTuReturn
} from '@/types/schemas/bang-ke-chung-tu.type';
import api from '@/lib/api';

const generateMockData = (): BangKeChungTuItem[] => {
  const documentTypes = ['HDB', 'PXK', 'PNK', 'UNC', 'KTT'];
  const accounts = ['111', '112', '131', '331', '511', '632', '711'];
  const customers = [
    { code: 'KH001', name: 'Công ty TNHH ABC Technology' },
    { code: 'KH002', name: 'Cửa hàng XYZ Trading' },
    { code: 'KH003', name: '<PERSON><PERSON><PERSON> nghiệp DEF Solutions' },
    { code: 'KH004', name: 'Công ty CP GHI Manufacturing' },
    { code: 'KH005', name: '<PERSON><PERSON><PERSON> đoàn JKL Group' }
  ];
  const departments = ['BP001', 'BP002', 'BP003', 'BP004'];
  const units = ['CN', 'BN', 'HN', 'SG'];
  const descriptions = [
    'Bán hàng phần mềm quản lý',
    'Thu tiền bán hàng',
    'Chi phí vận chuyển',
    'Thanh toán tiền mua hàng',
    'Thu lãi tiền gửi ngân hàng',
    'Chi phí quảng cáo',
    'Bán thiết bị văn phòng',
    'Thu tiền dịch vụ tư vấn'
  ];

  return Array.from({ length: 50 }, (_, index) => {
    const currentDate = new Date();
    const randomDays = Math.floor(Math.random() * 90); // Last 90 days
    const docDate = new Date(currentDate.getTime() - randomDays * 24 * 60 * 60 * 1000);
    const docDate0 = new Date(docDate.getTime() - Math.floor(Math.random() * 5) * 24 * 60 * 60 * 1000);

    const customer = customers[index % customers.length];
    const docType = documentTypes[index % documentTypes.length];
    const account = accounts[index % accounts.length];
    const department = departments[index % departments.length];
    const unit = units[index % units.length];
    const description = descriptions[index % descriptions.length];

    const amount = Math.floor(Math.random() * *********) + 1000000; // 1M to 100M
    const isDebit = Math.random() > 0.5;

    return {
      stt: index + 1,
      tk: account,
      id: `DOC${String(index + 1).padStart(6, '0')}`,
      unit_id: `UNIT${String(index + 1).padStart(3, '0')}`,
      ma_ct: docType,
      ngay_ct: docDate, // Keep as Date object for MUI DataGrid
      so_ct: `${docType}${String(index + 1).padStart(6, '0')}`,
      so_ct0: `${docType}0${String(index + 1).padStart(5, '0')}`,
      ngay_ct0: docDate0, // Keep as Date object for MUI DataGrid
      ma_kh: customer.code,
      tk_du: accounts[(index + 1) % accounts.length],
      nh_dk: `NH${String((index % 5) + 1).padStart(2, '0')}`,
      xgroup: `GRP${String((index % 10) + 1).padStart(2, '0')}`,
      ps_no: isDebit ? amount : 0,
      ps_co: !isDebit ? amount : 0,
      dien_giai: description,
      ma_bp: department,
      ma_vv: `VV${String((index % 8) + 1).padStart(3, '0')}`,
      ma_hd: `HD${String((index % 12) + 1).padStart(4, '0')}`,
      ma_ku: `KU${String((index % 6) + 1).padStart(3, '0')}`,
      ma_phi: `PHI${String((index % 4) + 1).padStart(2, '0')}`,
      ma_sp: `SP${String((index % 20) + 1).padStart(4, '0')}`,
      ma_lsx: `LSX${String((index % 15) + 1).padStart(4, '0')}`,
      ma_dtt: `DTT${String((index % 8) + 1).padStart(3, '0')}`,
      ma_cp0: `CP${String((index % 10) + 1).padStart(3, '0')}`,
      ma_unit: unit,
      ten_kh: customer.name
    };
  });
};

/**
 * Custom hook for managing BangKeChungTu (Document Listing) data
 *
 * This hook provides functionality to fetch document listing data
 * with mock support for testing and development purposes.
 */
export function useBangKeChungTu(searchParams: BangKeChungTuSearchFormValues): UseBangKeChungTuReturn {
  const [data, setData] = useState<BangKeChungTuItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BangKeChungTuSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BangKeChungTuResponse>('/tong-hop/tra-cuu/bang-ke-chung-tu/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
