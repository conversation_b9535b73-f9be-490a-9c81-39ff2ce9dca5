import { ApiResponse } from '../api.type';

export interface BangTinhPhanBoCCDCTTItem {
  id: string;
  ma_cc: string;
  ten_cc: string;
  ma_lcc: string;
  ten_lcc: string;
  ten_dvt: string;
  so_luong: number;
  ngay_kh0: string;
  so_ky_kh: number;
  ngay_kh_kt: string;
  tk_cc: string;
  ma_bp: string;
  systotal: number;
  [key: string]: any;
}

export interface BangTinhPhanBoCCDCTTResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BangTinhPhanBoCCDCTTItem[];
}

export type BangTinhPhanBoCCDCTTApiResponse = ApiResponse<BangTinhPhanBoCCDCTTItem>;

export interface UseBangTinhPhanBoCCDCTTReturn {
  data: BangTinhPhanBoCCDCTTItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: any) => Promise<void>;
  refreshData: () => Promise<void>;
}
