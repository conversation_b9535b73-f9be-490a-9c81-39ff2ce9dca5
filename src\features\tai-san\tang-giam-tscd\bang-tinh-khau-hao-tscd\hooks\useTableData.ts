import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { BangTinhKhauHaoTSCDItem } from '@/types/schemas';
import { formatDate } from '@/lib/formatUtils';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

// Extended interface for table display with total row support
interface BangTinhKhauHaoTSCDTableItem extends BangTinhKhauHaoTSCDItem {
  isTotal?: boolean;
}

export function useTableData(data: BangTinhKhauHaoTSCDItem[]): UseTableDataReturn {
  const tables = useMemo(() => {
    const totalNguyenGia = data.reduce((sum, item) => sum + (item.nguyen_gia || 0), 0);
    const totalGtDaKh = data.reduce((sum, item) => sum + (item.gt_da_kh || 0), 0);
    const totalGtClCk = data.reduce((sum, item) => sum + (item.gt_cl_ck || 0), 0);

    const totalRow: BangTinhKhauHaoTSCDTableItem = {
      id: 'total',
      stt: 0,
      ma_ts: '',
      ten_ts: `Tổng cộng`,
      ten_dvt: '',
      so_luong: 0,
      ma_lts: '',
      ten_lts: '',
      ngay_kh0: '',
      so_ky_kh: 0,
      ngay_kh_kt: '',
      nguyen_gia_nt: 0,
      gt_da_kh_dk_nt: 0,
      gt_cl_dk_nt: 0,
      gt_da_kh_nt: 0,
      gt_da_kh_lk_nt: 0,
      gt_cl_ck_nt: 0,
      ma_nt: '',
      ty_gia: 0,
      nguyen_gia: totalNguyenGia,
      gt_da_kh_dk: 0,
      gt_cl_dk: 0,
      gt_da_kh: totalGtDaKh,
      gt_da_kh_lk: 0,
      gt_cl_ck: totalGtClCk,
      tk_ts: '',
      tk_kh: '',
      tk_cp: '',
      ma_bp: '',
      ma_vv: '',
      ma_phi: '',
      systotal: 1,
      isTotal: true
    };

    const tableData: TableData[] = [
      {
        name: '',
        columns: [
          {
            field: 'stt',
            headerName: 'STT',
            width: 40,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.stt || '';
            }
          },
          {
            field: 'ma_ts',
            headerName: 'Mã tài sản',
            width: 100,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ma_ts || '';
            }
          },
          {
            field: 'ten_ts',
            headerName: 'Tên tài sản',
            width: 250
          },
          {
            field: 'ten_dvt',
            headerName: 'Đvt',
            width: 80,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ten_dvt || '';
            }
          },
          {
            field: 'so_luong',
            headerName: 'Số lượng',
            width: 100,
            type: 'number',
            renderCell: (params: any) => {
              if (params.row.isTotal) return '';
              return params.row.so_luong || 0;
            }
          },
          {
            field: 'ten_lts',
            headerName: 'Tên loại tài sản',
            width: 150,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ten_lts || '';
            }
          },
          {
            field: 'ngay_kh0',
            headerName: 'Ngày tính khấu hao',
            width: 130,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : formatDate(params.row.ngay_kh0) || '';
            }
          },
          {
            field: 'so_ky_kh',
            headerName: 'Số kỳ khấu hao',
            width: 100,
            type: 'number',
            renderCell: (params: any) => {
              if (params.row.isTotal) return '';
              return params.row.so_ky_kh || 0;
            }
          },
          {
            field: 'ngay_kh_kt',
            headerName: 'Ngày kết thúc khấu hao',
            width: 159,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : formatDate(params.row.ngay_kh_kt) || '';
            }
          },
          {
            field: 'nguyen_gia',
            headerName: 'Nguyên giá',
            width: 159,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.nguyen_gia || 0;
              return value.toLocaleString('vi-VN');
            }
          },
          {
            field: 'gt_da_kh_dk',
            headerName: 'GT đã KH đầu kỳ',
            width: 159,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.gt_da_kh_dk || 0;
              return value.toLocaleString('vi-VN');
            }
          },
          {
            field: 'gt_cl_dk',
            headerName: 'GT còn lại đầu kỳ',
            width: 159,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.gt_cl_dk || 0;
              return value.toLocaleString('vi-VN');
            }
          },
          {
            field: 'gt_da_kh',
            headerName: 'GT khấu hao trong kỳ',
            width: 159,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.gt_da_kh || 0;
              return value.toLocaleString('vi-VN');
            }
          },
          {
            field: 'gt_da_kh_lk',
            headerName: 'GT đã KH lũy kế',
            width: 159,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.gt_da_kh_lk || 0;
              return value.toLocaleString('vi-VN');
            }
          },
          {
            field: 'gt_cl_ck',
            headerName: 'GT còn lại cuối kỳ',
            width: 159,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.gt_cl_ck || 0;
              return value.toLocaleString('vi-VN');
            }
          },
          {
            field: 'tk_ts',
            headerName: 'TK tài sản',
            width: 80,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.tk_ts || '';
            }
          },
          {
            field: 'tk_kh',
            headerName: 'TK khấu hao',
            width: 80,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.tk_kh || '';
            }
          },
          {
            field: 'tk_cp',
            headerName: 'TK chi phí',
            width: 80,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.tk_cp || '';
            }
          },
          {
            field: 'ma_bp',
            headerName: 'Mã bộ phận',
            width: 159,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ma_bp || '';
            }
          }
        ],
        rows: [totalRow, ...data]
      }
    ];

    return tableData;
  }, [data]);

  const handleRowClick = (params: any) => {};

  return {
    tables,
    handleRowClick
  };
}
