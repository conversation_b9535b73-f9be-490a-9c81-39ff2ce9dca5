import { <PERSON><PERSON>, Printer, Refresh<PERSON><PERSON>, Search, Sheet } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button/index';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onPrintClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportDataClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  className?: string;
  searchParams?: any;
}

const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onRefreshClick,
  onPrintClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  className,
  searchParams
}) => {
  return (
    <AritoActionBar
      className={className}
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>Sổ quỹ</h1>
          <div className='text-[10px] text-gray-500'>
            <p className='font-semibold'>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              <span>Tài khoản: {searchParams?.tai_khoan || '1121'}</span>
              <span>, từ ngày </span>
              <span>{searchParams?.ngay_tu || '01/04/2025'}</span>
              <span> đến ngày </span>
              <span>{searchParams?.ngay_den || '22/04/2025'}</span>
            </p>
          </div>
        </div>
      }
    >
      {onSearchClick && (
        <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='secondary' />
      )}
      <AritoActionButton title='In ấn' icon={Printer} onClick={onPrintClick} />
      {onRefreshClick && (
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} variant='secondary' />
      )}
      {onFixedColumnsClick && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} variant='secondary' />
      )}
      {onExportDataClick && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
