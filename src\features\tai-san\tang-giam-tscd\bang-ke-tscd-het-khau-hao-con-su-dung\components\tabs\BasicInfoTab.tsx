import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const BasicInfo: React.FC = () => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày báo cáo:</Label>
          <div>
            <FormField name='reportDate' type='date' label='' className='w-[206px]' />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
