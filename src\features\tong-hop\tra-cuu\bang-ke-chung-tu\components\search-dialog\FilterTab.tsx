import {
  QUERY_KEYS,
  dotThanhToanSearchColumns,
  kheUocSearchColumns,
  boPhanSearchColumns,
  vuViecSearchColumns,
  hopDongSearchColumns,
  phiSearchColumns,
  sanPhamSearchColumns,
  lenhSanXuatSearchColumns
} from '@/constants';
import { BoPhan, DotThanhToan, HopDong, KheUoc, Phi, VatTu, VuViec } from '@/types/schemas';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { SearchDialogState, SearchDialogActions } from '../../hooks';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface FilterTabProps {
  formMode: FormMode;
  searchState: {
    state: SearchDialogState;
    actions: SearchDialogActions;
  };
}

export const FilterTab: React.FC<FilterTabProps> = ({ searchState: { state, actions } }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Bộ phận</Label>
          <SearchField<BoPhan>
            type='text'
            name='ma_bp'
            searchColumns={boPhanSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
            dialogTitle='Danh mục bộ phận'
            columnDisplay='ma_bp'
            displayRelatedField='ten_bp'
            classNameRelatedField='w-full'
            onRowSelection={actions.setBoPhan}
            value={state.boPhan?.ma_bp || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Danh mục vụ việc</Label>
          <SearchField<VuViec>
            type='text'
            name='ma_vv'
            searchColumns={vuViecSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
            dialogTitle='Danh mục vụ việc'
            columnDisplay='ma_vu_viec'
            displayRelatedField='ten_vu_viec'
            classNameRelatedField='w-full'
            onRowSelection={actions.setVuViec}
            value={state.vuViec?.ma_vu_viec || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Hợp đồng</Label>
          <SearchField<HopDong>
            type='text'
            name='ma_hd'
            searchColumns={hopDongSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
            dialogTitle='Danh mục hợp đồng'
            columnDisplay='ma_hd'
            displayRelatedField='ten_hd'
            classNameRelatedField='w-full'
            onRowSelection={actions.setHopDong}
            value={state.hopDong?.ma_hd || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Đợt thanh toán</Label>
          <SearchField<DotThanhToan>
            type='text'
            name='ma_dtt'
            searchColumns={dotThanhToanSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
            dialogTitle='Danh mục đợt thanh toán'
            columnDisplay='ma_dtt'
            displayRelatedField='ten_dtt'
            classNameRelatedField='w-full'
            onRowSelection={actions.setDotThanhToan}
            value={state.dotThanhToan?.ma_dtt || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Khế ước</Label>
          <SearchField<KheUoc>
            type='text'
            name='ma_ku'
            searchColumns={kheUocSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
            dialogTitle='Danh mục khế ước'
            columnDisplay='ma_ku'
            displayRelatedField='ten_ku'
            classNameRelatedField='w-full'
            onRowSelection={actions.setKheUoc}
            value={state.kheUoc?.ma_ku || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Phí</Label>
          <SearchField<Phi>
            type='text'
            name='ma_phi'
            searchColumns={phiSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.PHI}/`}
            dialogTitle='Danh mục phí'
            columnDisplay='ma_phi'
            displayRelatedField='ten_phi'
            classNameRelatedField='w-full'
            onRowSelection={actions.setPhi}
            value={state.phi?.ma_phi || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Sản phẩm</Label>
          <SearchField<VatTu>
            type='text'
            name='ma_sp'
            searchColumns={sanPhamSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
            dialogTitle='Danh mục sản phẩm'
            columnDisplay='ma_vt'
            displayRelatedField='ten_vt'
            classNameRelatedField='w-full'
            onRowSelection={actions.setSanPham}
            value={state.sanPham?.ma_vt || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Lệnh sản xuất</Label>
          <SearchField<any>
            type='text'
            name='ma_lsx'
            searchColumns={lenhSanXuatSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}/`}
            dialogTitle='Lệnh sản xuất'
            columnDisplay='ma_lsx'
            displayRelatedField='ten_lsx'
            classNameRelatedField='w-full'
            onRowSelection={actions.setLenhSanXuat}
            value={state.lenhSanXuat?.ma_lsx || ''}
          />
        </div>
      </div>
    </div>
  );
};
