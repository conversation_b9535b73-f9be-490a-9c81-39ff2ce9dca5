import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { BaoCaoTongHopBanHangThuanItem } from '@/types/schemas';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(data: BaoCaoTongHopBanHangThuanItem[]): UseTableDataReturn {
  const tables = useMemo(() => {
    const tableData: TableData[] = [
      {
        name: '',
        columns: [
          {
            field: 'ma_vt',
            headerName: 'Mã vật tư',
            width: 150,
            renderCell: (params: any) => {
              return params.row.ma_vt || '';
            }
          },
          {
            field: 'ten_vt',
            headerName: 'Tên vật tư',
            width: 200,
            renderCell: (params: any) => {
              return params.row.ten_vt || '';
            }
          },
          {
            field: 'dvt',
            headerName: 'Đvt',
            width: 80,
            renderCell: (params: any) => {
              return params.row.dvt || '';
            }
          },
          {
            field: 'tien_ban',
            headerName: 'Doanh thu',
            width: 150,
            type: 'number',
            renderCell: (params: any) => {
              return params.row.tien_ban ? params.row.tien_ban.toLocaleString('vi-VN') : '0';
            }
          },
          {
            field: 'tien_tl',
            headerName: 'Trả lại',
            width: 150,
            type: 'number',
            renderCell: (params: any) => {
              return params.row.tien_tl ? params.row.tien_tl.toLocaleString('vi-VN') : '0';
            }
          },
          {
            field: 'tien_cl',
            headerName: 'Dt còn lại',
            width: 150,
            type: 'number',
            renderCell: (params: any) => {
              return params.row.tien_cl ? params.row.tien_cl.toLocaleString('vi-VN') : '0';
            }
          }
        ],
        rows: (data || []).map(item => ({
          id: item.id,
          ma_vt: item.ma_vt,
          ten_vt: item.ten_vt,
          dvt: item.dvt,
          tien_ban: item.tien_ban,
          tien_tl: item.tien_tl,
          tien_cl: item.tien_cl
        }))
      }
    ];

    return tableData;
  }, [data]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick
  };
}
