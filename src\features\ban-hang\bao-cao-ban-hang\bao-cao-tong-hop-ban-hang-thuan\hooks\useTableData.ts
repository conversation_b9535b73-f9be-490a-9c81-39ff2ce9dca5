import React, { useState, useEffect } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(): UseTableDataReturn {
  const [tables, setTables] = useState<TableData[]>([
    {
      name: '',
      columns: [
        { field: 'materialCode', headerName: 'Mã vật tư', width: 150 },
        { field: 'materialName', headerName: 'Tên vật tư', width: 200 },
        { field: 'unit', headerName: 'Đvt', width: 80 },
        { field: 'revenue', headerName: 'Doanh thu', width: 150, type: 'number' },
        { field: 'returned', headerName: 'Trả lại', width: 150, type: 'number' },
        { field: 'remainingRevenue', headerName: 'Dt còn lại', width: 150, type: 'number' },
        { field: 'inventoryAccount', headerName: 'Tk kho', width: 120 }
      ],
      rows: []
    }
  ]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,

    handleRowClick
  };
}
