import React, { useState } from 'react';
import { BottomBar, AritoModal, AritoIcon } from '@/components/custom/arito';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoForm } from '@/components/custom/arito/form';
interface Props {
  formMode: 'add' | 'edit' | 'view';
}

export const AccountTab = ({ formMode }: Props) => {
  const [showCustomerInfoForm, setShowCustomerInfoForm] = useState(false);
  const [customerInfoData, setCustomerInfoData] = useState<any[]>([]);

  const handleOpenForm = () => {
    setShowCustomerInfoForm(true);
  };

  const handleCloseForm = () => {
    setShowCustomerInfoForm(false);
  };

  const handleSubmitForm = (data: any) => {
    console.log('Form submitted:', data);
    setShowCustomerInfoForm(false);
  };

  const handleTableDataChange = (data: any[]) => {
    setCustomerInfoData(data);
  };

  return (
    <div className='grid grid-cols-1 gap-x-8 space-y-2 p-4 lg:grid-cols-1 lg:space-y-0'>
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Mô tả'
        name='description'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Địa chỉ giao hàng'
        name='delivery_address'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Lĩnh vực hoạt động'
        name='business_field'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Giới hạn tín dụng'
        name='credit_limit'
        labelClassName='min-w-[180px]'
        type='number'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Số tài khoản ngân hàng'
        name='bank_account'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Tên ngân hàng'
        name='bank_name'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Chi nhánh ngân hàng'
        name='bank_branch'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Ghi chú'
        name='notes'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <button
        type='button'
        onClick={handleOpenForm}
        className='text-blue-600 hover:text-blue-800 hover:underline'
        disabled={formMode === 'view'}
      >
        Khai báo thông tin khách hàng
      </button>

      <AritoModal
        open={showCustomerInfoForm}
        onClose={handleCloseForm}
        title='Sửa thuộc tính'
        titleIcon={<AritoIcon icon={12} />}
        maxWidth='md'
      >
        <div className='flex size-full flex-col overflow-hidden'>
          <div className='max-h-[calc(100vh-120px)] flex-1 overflow-auto'>
            <AritoForm<any>
              mode={formMode}
              hasAritoActionBar={false}
              className='!static !w-full'
              onSubmit={handleSubmitForm}
              onClose={handleCloseForm}
              headerFields={
                <div className='w-full p-4'>
                  <div className='grid grid-cols-1 gap-4'>
                    <div className='flex items-center'>
                      <span className='w-[160px] text-sm font-medium'>Loại thuộc tính:</span>
                      <span className='text-sm'>Danh mục khách hàng/nhà cung cấp</span>
                    </div>
                  </div>
                </div>
              }
              tabs={[
                {
                  id: 'chi-tiet',
                  label: 'Chi tiết',
                  component: (
                    <div className='p-4'>
                      <div className='text-sm text-gray-600'>Nội dung chi tiết sẽ được hiển thị ở đây</div>
                    </div>
                  )
                }
              ]}
            />
          </div>

          <BottomBar
            mode={formMode}
            onSubmit={() => {}}
            onClose={() => {
              setShowCustomerInfoForm(false);
            }}
          />
        </div>
      </AritoModal>
    </div>
  );
};
