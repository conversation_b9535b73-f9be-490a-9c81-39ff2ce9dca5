/**
 * TypeScript interface for BangTinhKhauHaoTSCD model
 *
 * This interface represents the structure of the BangTinhKhauHaoTSCD model from the backend.
 * It defines fixed asset depreciation calculation table data.
 */

import { ApiResponse } from '../api.type';

export interface BangTinhKhauHaoTSCDItem {
  /**
   * Sequential number
   */
  stt: number;

  /**
   * Asset code
   */
  ma_ts: string;

  /**
   * Asset name
   */
  ten_ts: string;

  /**
   * Unit name
   */
  ten_dvt: string;

  /**
   * Quantity
   */
  so_luong: number;

  /**
   * Asset type code
   */
  ma_lts: string;

  /**
   * Asset type name
   */
  ten_lts: string;

  /**
   * Depreciation start date
   */
  ngay_kh0: string;

  /**
   * Number of depreciation periods
   */
  so_ky_kh: number;

  /**
   * Depreciation end date
   */
  ngay_kh_kt: string;

  /**
   * Original price in foreign currency
   */
  nguyen_gia_nt: number;

  /**
   * Accumulated depreciation at beginning of period (foreign currency)
   */
  gt_da_kh_dk_nt: number;

  /**
   * Remaining value at beginning of period (foreign currency)
   */
  gt_cl_dk_nt: number;

  /**
   * Depreciation in period (foreign currency)
   */
  gt_da_kh_nt: number;

  /**
   * Accumulated depreciation (foreign currency)
   */
  gt_da_kh_lk_nt: number;

  /**
   * Remaining value at end of period (foreign currency)
   */
  gt_cl_ck_nt: number;

  /**
   * Currency code
   */
  ma_nt: string;

  /**
   * Exchange rate
   */
  ty_gia: number;

  /**
   * Original price in VND
   */
  nguyen_gia: number;

  /**
   * Accumulated depreciation at beginning of period (VND)
   */
  gt_da_kh_dk: number;

  /**
   * Remaining value at beginning of period (VND)
   */
  gt_cl_dk: number;

  /**
   * Depreciation in period (VND)
   */
  gt_da_kh: number;

  /**
   * Accumulated depreciation (VND)
   */
  gt_da_kh_lk: number;

  /**
   * Remaining value at end of period (VND)
   */
  gt_cl_ck: number;

  /**
   * Asset account
   */
  tk_ts: string;

  /**
   * Depreciation account
   */
  tk_kh: string;

  /**
   * Expense account
   */
  tk_cp: string;

  /**
   * Department code
   */
  ma_bp: string;

  /**
   * Work code
   */
  ma_vv: string;

  /**
   * Fee code
   */
  ma_phi: string;

  /**
   * System total indicator
   */
  systotal: number;

  /**
   * Unique identifier for table display
   */
  id?: string;

  /**
   * Flag to indicate if this is a total row
   */
  isTotal?: boolean;
}

/**
 * API Response type for BangTinhKhauHaoTSCD
 */
export type BangTinhKhauHaoTSCDResponse = ApiResponse<BangTinhKhauHaoTSCDItem>;

/**
 * Hook return type for useBangTinhKhauHaoTSCD
 */
export interface UseBangTinhKhauHaoTSCDReturn {
  data: BangTinhKhauHaoTSCDItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: any) => Promise<void>;
  refreshData: () => Promise<void>;
}
