import { useState, useMemo, useCallback } from 'react';
import { useBaoCaoTinhTrangDeNghiThanhToan } from './useBaoCaoTinhTrangDeNghiThanhToan';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { SearchFormValues, initialSearchValues } from '../schema';
import { getDataTableColumns } from '../cols-definition';

export interface UseReportDataReturn {
  initialSearchDialogOpen: boolean;
  editPrintTemplateDialogOpen: boolean;
  showTable: boolean;
  searchParams: SearchFormValues;

  handleInitialSearchClose: () => void;
  handleInitialSearch: (data: SearchFormValues) => void;
  handleSearchClick: () => void;
  handleEditPrintTemplateClick: () => void;
  handleClosePrintTemplateDialog: () => void;
  handleSavePrintTemplate: (data: any) => void;

  tables: TableData[];
  handleRowClick: (params: any) => void;
  isLoading: boolean;

  handleRefreshClick: () => void;
  handleFixedColumnsClick: () => void;
  handleExportDataClick: () => void;
}

export function useReportData(): UseReportDataReturn {
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [editPrintTemplateDialogOpen, setEditPrintTemplateDialogOpen] = useState(false);
  const [showTable, setShowTable] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchFormValues>(initialSearchValues);

  const { data, isLoading, fetchData, refreshData } = useBaoCaoTinhTrangDeNghiThanhToan(searchParams);

  const tables = useMemo(() => {
    if (!data || data.length === 0) return [];

    const tableData: TableData[] = [
      {
        name: '',
        columns: getDataTableColumns,
        rows: data
      }
    ];

    return tableData;
  }, [data]);

  const handleInitialSearchClose = useCallback(() => {
    setInitialSearchDialogOpen(false);
  }, []);

  const handleInitialSearch = useCallback(
    async (data: SearchFormValues) => {
      setSearchParams(data);
      setInitialSearchDialogOpen(false);
      setShowTable(true);
      await fetchData(data);
    },
    [fetchData]
  );

  const handleSearchClick = useCallback(() => {
    setInitialSearchDialogOpen(true);
  }, []);

  const handleEditPrintTemplateClick = useCallback(() => {
    setEditPrintTemplateDialogOpen(true);
  }, []);

  const handleClosePrintTemplateDialog = useCallback(() => {
    setEditPrintTemplateDialogOpen(false);
  }, []);

  const handleSavePrintTemplate = useCallback((data: any) => {
    console.log('Saving print template:', data);
    setEditPrintTemplateDialogOpen(false);
  }, []);

  const handleRowClick = useCallback((params: any) => {
    console.log('Row clicked:', params);
  }, []);

  const handleRefreshClick = useCallback(async () => {
    await refreshData();
  }, [refreshData]);

  const handleFixedColumnsClick = useCallback(() => {
    console.log('Fixed columns clicked');
  }, []);

  const handleExportDataClick = useCallback(() => {
    console.log('Export data clicked');
  }, []);

  return {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate,

    tables,
    handleRowClick,
    isLoading,

    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick
  };
}
