import React, { useState, useEffect } from 'react';
import { useFullyDepreciatedAssetsData } from './useFullyDepreciatedAssetsData';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { fullyDepreciatedAssetsColumns } from '../cols-definition';
import { SearchFormValues } from '../schema';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
}

export function useTableData(searchParams: SearchFormValues): UseTableDataReturn {
  const { data, isLoading, error, refreshData } = useFullyDepreciatedAssetsData(searchParams);

  const [tables, setTables] = useState<TableData[]>([
    {
      name: '',
      columns: fullyDepreciatedAssetsColumns,
      rows: data
    }
  ]);

  React.useEffect(() => {
    setTables(prevTables => {
      const updatedTables = [...prevTables];
      updatedTables[0].rows = data;
      return updatedTables;
    });
  }, [data]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData
  };
}
