/**
 * TypeScript interfaces for ToKhaiThueGTGT (VAT Tax Declaration) model
 *
 * This interface represents the structure of the ToKhaiThueGTGT model from the backend.
 * It defines VAT tax declarations and related data structures.
 */

import { ApiResponse } from '../api.type';

// VAT Tax Declaration Item
export interface VatTaxDeclarationItem {
  id: string;
  stt_in: string;
  chi_tieu: string;
  ma_so: string;
  doanh_so: number;
  thue: number;
}

// VAT Tax Declaration PL43 Item
export interface VatTaxDeclarationPL43Item {
  id: string;
  stt_in: string;
  ten_hhdv: string;
  ma_so: string;
  tien_chua_thue: number;
  thue_suat: number;
  thue_suat_duoc_giam: number;
  thue_duoc_giam: number;
}

// Bang Ke Ban Ra Item
export interface BangKeBanRaItem {
  id: string;
  so_hoa_don: string;
  ngay_hoa_don: Date | string; // Support both Date objects and strings
  ky_hieu: string;
  ten_khach_hang: string;
  ten_mat_hang: string;
  ma_so_thue: string;
  tien_ban: number;
  thue_suat: number;
  tien_thue: number;
  ghi_chu: string;
}

// PL142 Item
export interface PL142Item {
  id: string;
  ten_hang_hoa_dich_vu: string;
  ma_so: string;
  tien_chua_thue_nt: number;
  tien_chua_thue: number;
  thue_suat_quy_dinh: number;
  thue_suat_sau_giam: number;
  thue_duoc_giam_nt: number;
  thue_duoc_giam: number;
  thue_gtgt_cua_hh_dv_nt: number;
  thue_gtgt_cua_hh_dv: number;
}

// Bang Ke Mua Vao Item
export interface BangKeMuaVaoItem {
  id: string;
  so_hoa_don: string;
  ngay_hoa_don: Date | string; // Support both Date objects and strings
  so_chung_tu: string;
  ky_hieu: string;
  ten_khach_hang: string;
  ma_so_thue: string;
  ten_mat_hang: string;
  tien_chua_thue: number;
  thue_suat: number;
  tien_thue: number;
  tong_tien: number;
  ghi_chu: string;
}

// Main ToKhaiThueGTGT Item
export interface ToKhaiThueGTGTItem {
  id: string;
  loai: string;
  don_vi: string;
  bo_phan: string;
  so_ct: string;
  ngay_ct: Date | string; // Support both Date objects and strings
  dien_giai: string;
  thoi_gian_to_khai: string;
  thang: number;
  nam: number;
  loai_to_khai: string;
  chon_bao_cao: string;
  mau_bao_cao: string;
  status: string;
  trang_thai: string;
  ngay_tao: Date | string; // Support both Date objects and strings
  nguoi_tao: string;
}

// API Response types
export type VatTaxDeclarationResponse = ApiResponse<VatTaxDeclarationItem>;
export type VatTaxDeclarationPL43Response = ApiResponse<VatTaxDeclarationPL43Item>;
export type BangKeBanRaResponse = ApiResponse<BangKeBanRaItem>;
export type PL142Response = ApiResponse<PL142Item>;
export type BangKeMuaVaoResponse = ApiResponse<BangKeMuaVaoItem>;
export type ToKhaiThueGTGTResponse = ApiResponse<ToKhaiThueGTGTItem>;

// Search form values interface
export interface ToKhaiThueGTGTSearchFormValues {
  loai?: string;
  don_vi?: string;
  bo_phan?: string;
  so_ct?: string;
  ngay_ct?: Date;
  dien_giai?: string;
  thoi_gian_to_khai?: string;
  thang?: number;
  nam?: number;
  loai_to_khai?: string;
  chon_bao_cao?: string;
  mau_bao_cao?: string;
  [key: string]: any;
}

// Hook return types
export interface UseToKhaiThueGTGTReturn {
  data: ToKhaiThueGTGTItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: ToKhaiThueGTGTSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

export interface UseVatTaxDeclarationReturn {
  data: VatTaxDeclarationItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: ToKhaiThueGTGTSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

export interface UseBangKeBanRaReturn {
  data: BangKeBanRaItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: ToKhaiThueGTGTSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

export interface UseBangKeMuaVaoReturn {
  data: BangKeMuaVaoItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: ToKhaiThueGTGTSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

export interface UsePL142Return {
  data: PL142Item[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: ToKhaiThueGTGTSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
