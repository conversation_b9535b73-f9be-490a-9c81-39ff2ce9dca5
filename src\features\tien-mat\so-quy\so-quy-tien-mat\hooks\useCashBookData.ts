import { useState, useCallback, useEffect } from 'react';
import { SearchFormValues } from '../schemas';
import api from '@/lib/api';

// Types for cash book data
export interface CashBookItem {
  id: string;
  don_vi: string;
  ngay_chung_tu: string;
  so_chung_tu: string;
  ma_khach_hang: string;
  ten_khach_hang: string;
  dien_giai: string;
  tk_doi_ung: string;
  ngoai_te: string;
  ty_gia: number;
  ps_no: number;
  ps_co: number;
  so_du: number;
  bo_phan: string;
  vu_viec: string;
  hop_dong: string;
  dot_thanh_toan: string;
  khe_uoc: string;
  phi: string;
  san_pham: string;
  lenh_san_xuat: string;
  cp_khong_hop_le: string;
  ma_chung_tu: string;
}

export interface CashBookResponse {
  results: CashBookItem[];
  total: number;
}

export interface UseCashBookDataReturn {
  data: CashBookItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues | null) => Promise<void>;
  refreshData: () => Promise<void>;
}

// Generate mock data for cash book
const generateMockData = (): CashBookItem[] => {
  return [
    {
      id: '1',
      don_vi: 'CN001',
      ngay_chung_tu: '01/04/2025',
      so_chung_tu: 'PT001',
      ma_khach_hang: 'KH001',
      ten_khach_hang: 'ABC Company',
      dien_giai: 'Thu tiền bán hàng khách hàng ABC Company',
      tk_doi_ung: '131',
      ngoai_te: 'VND',
      ty_gia: 1,
      ps_no: 15000000,
      ps_co: 0,
      so_du: 15000000,
      bo_phan: 'BP001',
      vu_viec: 'VV001',
      hop_dong: 'HD001',
      dot_thanh_toan: 'DT001',
      khe_uoc: 'KU001',
      phi: 'PH001',
      san_pham: 'SP001',
      lenh_san_xuat: 'LSX001',
      cp_khong_hop_le: '',
      ma_chung_tu: 'MCT001'
    },
    {
      id: '2',
      don_vi: 'CN001',
      ngay_chung_tu: '02/04/2025',
      so_chung_tu: 'PC001',
      ma_khach_hang: 'NCC001',
      ten_khach_hang: 'Công ty TNHH Văn phòng phẩm XYZ',
      dien_giai: 'Chi tiền mua văn phòng phẩm',
      tk_doi_ung: '642',
      ngoai_te: 'VND',
      ty_gia: 1,
      ps_no: 0,
      ps_co: 850000,
      so_du: 14150000,
      bo_phan: 'BP002',
      vu_viec: 'VV002',
      hop_dong: 'HD002',
      dot_thanh_toan: 'DT002',
      khe_uoc: 'KU002',
      phi: 'PH002',
      san_pham: 'SP002',
      lenh_san_xuat: 'LSX002',
      cp_khong_hop_le: '',
      ma_chung_tu: 'MCT002'
    },
    {
      id: '3',
      don_vi: 'CN001',
      ngay_chung_tu: '03/04/2025',
      so_chung_tu: 'PT002',
      ma_khach_hang: 'NH001',
      ten_khach_hang: 'Ngân hàng BIDV',
      dien_giai: 'Thu tiền lãi ngân hàng tháng 3',
      tk_doi_ung: '515',
      ngoai_te: 'VND',
      ty_gia: 1,
      ps_no: 450000,
      ps_co: 0,
      so_du: 14600000,
      bo_phan: 'BP003',
      vu_viec: 'VV003',
      hop_dong: 'HD003',
      dot_thanh_toan: 'DT003',
      khe_uoc: 'KU003',
      phi: 'PH003',
      san_pham: 'SP003',
      lenh_san_xuat: 'LSX003',
      cp_khong_hop_le: '',
      ma_chung_tu: 'MCT003'
    },
    {
      id: '4',
      don_vi: 'CN001',
      ngay_chung_tu: '04/04/2025',
      so_chung_tu: 'PC002',
      ma_khach_hang: 'NS001',
      ten_khach_hang: 'Phòng Nhân sự',
      dien_giai: 'Chi tiền lương nhân viên tháng 3',
      tk_doi_ung: '334',
      ngoai_te: 'VND',
      ty_gia: 1,
      ps_no: 0,
      ps_co: 12500000,
      so_du: 2100000,
      bo_phan: 'BP004',
      vu_viec: 'VV004',
      hop_dong: 'HD004',
      dot_thanh_toan: 'DT004',
      khe_uoc: 'KU004',
      phi: 'PH004',
      san_pham: 'SP004',
      lenh_san_xuat: 'LSX004',
      cp_khong_hop_le: '',
      ma_chung_tu: 'MCT004'
    },
    {
      id: '5',
      don_vi: 'CN001',
      ngay_chung_tu: '05/04/2025',
      so_chung_tu: 'PT003',
      ma_khach_hang: 'KH002',
      ten_khach_hang: 'DEF Ltd',
      dien_giai: 'Thu tiền bán hàng khách hàng DEF Ltd',
      tk_doi_ung: '131',
      ngoai_te: 'USD',
      ty_gia: 24500,
      ps_no: 22000000,
      ps_co: 0,
      so_du: 24100000,
      bo_phan: 'BP005',
      vu_viec: 'VV005',
      hop_dong: 'HD005',
      dot_thanh_toan: 'DT005',
      khe_uoc: 'KU005',
      phi: 'PH005',
      san_pham: 'SP005',
      lenh_san_xuat: 'LSX005',
      cp_khong_hop_le: '',
      ma_chung_tu: 'MCT005'
    }
  ];
};

/**
 * Custom hook for managing Cash Book data
 *
 * This hook provides functionality to fetch cash book data
 * with mock support for testing and development purposes.
 */
export function useCashBookData(searchParams: SearchFormValues): UseCashBookDataReturn {
  const [data, setData] = useState<CashBookItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues | null) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<CashBookResponse>('/tien-mat/so-quy/so-quy-tien-mat/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
