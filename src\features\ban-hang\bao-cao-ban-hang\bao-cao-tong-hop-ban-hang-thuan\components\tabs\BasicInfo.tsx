import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { Label } from '@/components/ui/label';

const BasicInfo: React.FC = () => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ng<PERSON>y từ/đến:</Label>
          <div>
            <AritoFormDateRangeDropdown fromDateName='ngay_ct1' toDateName='ngay_ct2' />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
