import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS, boPhanSearchColumns } from '@/constants';
import { STATUS_OPTIONS } from '../../cols-definition';
import { Label } from '@/components/ui/label';
import { Bo<PERSON>han } from '@/types/schemas';
import { FormMode } from '@/types/form';

interface GereralInfoTabProps {
  formMode: FormMode;
  boPhan: BoPhan | null;
  setBoPhan: (boPhan: BoPhan) => void;
}

export const GeneralInfoTab: React.FC<GereralInfoTabProps> = ({ formMode, boPhan, setBoPhan }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã nhà cung cấp</Label>
          <SearchField<BoPhan>
            type='text'
            name='ma_bp'
            searchColumns={boPhanSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
            dialogTitle='Danh mục đối tượng'
            columnDisplay='ma_bp'
            displayRelatedField='ten_bp'
            classNameRelatedField='w-full'
            onRowSelection={setBoPhan}
            value={boPhan?.ma_bp || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Trạng thái</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='status'
              disabled={formMode === 'view'}
              options={STATUS_OPTIONS}
              defaultValue={'10'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='mau_bc'
              disabled={formMode === 'view'}
              options={[
                { label: 'Mẫu tiền chuẩn', value: '20' },
                { label: 'Mẫu ngoại tệ', value: '30' }
              ]}
              defaultValue={'20'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
