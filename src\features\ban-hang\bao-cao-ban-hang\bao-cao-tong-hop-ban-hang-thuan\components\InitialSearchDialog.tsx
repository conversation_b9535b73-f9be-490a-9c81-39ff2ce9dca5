import React, { useState } from 'react';

import { But<PERSON> } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';
import { searchSchema, initialValues } from '../schemas';
import { DetailsTab, BasicInfo, OtherTab } from './tabs';
import AritoIcon from '@/components/custom/arito/icon';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const [formValues, setFormValues] = useState(initialValues);

  const handleSubmit = (data: any) => {
    console.log('Form submitted with data:', data); // Debug log
    onSearch(data);
    onClose();
  };

  const handleDirectSubmit = () => {
    console.log('Direct submit with formValues:', formValues); // Debug log
    // Trigger form submit instead of using state
    const form = document.querySelector('form');
    if (form) {
      form.requestSubmit();
    } else {
      // Fallback to using current formValues
      onSearch(formValues);
      onClose();
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Báo cáo tổng hợp bán hàng thuần'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
      actions={
        <>
          <Button variant='contained' className='bg-[#26827C]' onClick={handleDirectSubmit}>
            <AritoIcon icon={884} />
            <span className='ml-1'>Đồng ý</span>
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} />
            <span className='ml-1'>Huỷ</span>
          </Button>
        </>
      }
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={data => {
          setFormValues(data);
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailsTab />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
