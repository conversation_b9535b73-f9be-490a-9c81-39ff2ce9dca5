import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

const DetailsTab = () => {
  return (
    <div className='w-[800px] min-w-[800px] space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        {/* Mã khách hàng */}
        <div className='flex items-center gap-1'>
          <Label className='w-40 min-w-40'>Mã khách hàng:</Label>
          <div className='w-40'>
            <SearchField
              type='text'
              columnDisplay='customer_code'
              displayRelatedField='customer_name'
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
              searchColumns={[
                { field: 'customer_code', headerName: 'Mã khách hàng', width: 150 },
                { field: 'customer_name', headerName: 'Tên khách hàng', width: 200 },
                { field: 'phone', headerName: 'Số điện thoại', width: 150 },
                { field: 'email', headerName: 'Email', width: 200 }
              ]}
              dialogTitle='Danh mục khách hàng'
            />
          </div>
        </div>
        <div className='flex items-center pt-1'>
          <Label className='w-40 min-w-40'>Nhóm khách hàng:</Label>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <div className='w-40'>
                <SearchField
                  type='text'
                  displayRelatedField='ten_phan_nhom'
                  columnDisplay='ten_phan_nhom'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=KH1`}
                  searchColumns={[
                    { field: 'ma_nhom', headerName: 'Mã nhóm', width: 120 },
                    { field: 'ten_phan_nhom', headerName: 'Tên nhóm', width: 200 }
                  ]}
                  dialogTitle='Danh mục nhóm khách hàng 1'
                />
              </div>
              <div className='w-40'>
                <SearchField
                  type='text'
                  displayRelatedField='ten_phan_nhom'
                  columnDisplay='ten_phan_nhom'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=KH2`}
                  searchColumns={[
                    { field: 'ma_nhom', headerName: 'Mã nhóm', width: 120 },
                    { field: 'ten_phan_nhom', headerName: 'Tên nhóm', width: 200 }
                  ]}
                  dialogTitle='Danh mục nhóm khách hàng 2'
                />
              </div>
              <div className='w-40'>
                <SearchField
                  type='text'
                  displayRelatedField='ten_phan_nhom'
                  columnDisplay='ten_phan_nhom'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=KH3`}
                  searchColumns={[
                    { field: 'ma_nhom', headerName: 'Mã nhóm', width: 120 },
                    { field: 'ten_phan_nhom', headerName: 'Tên nhóm', width: 200 }
                  ]}
                  dialogTitle='Danh mục nhóm khách hàng 3'
                />
              </div>
            </div>
          </div>
        </div>
        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Khu vực:</Label>
            <div className='w-40'>
              <SearchField
                type='text'
                displayRelatedField='rgname'
                columnDisplay='rgname'
                searchEndpoint={`/${QUERY_KEYS.KHU_VUC}`}
                searchColumns={[
                  { field: 'rg_code', headerName: 'Mã khu vực', width: 120 },
                  { field: 'rgname', headerName: 'Tên khu vực', width: 200 }
                ]}
                dialogTitle='Danh mục khu vực'
              />
            </div>
          </div>
        </div>
        <div className='flex items-center gap-1 gap-x-5'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Mã vật tư:</Label>
            <div className='w-40'>
              <SearchField
                type='text'
                displayRelatedField='ten_vt'
                columnDisplay='ma_vt'
                searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
                searchColumns={[
                  { field: 'ma_vt', headerName: 'Mã vật tư', width: 120 },
                  { field: 'ten_vt', headerName: 'Tên vật tư', width: 200 },
                  { field: 'dvt', headerName: 'Đvt', width: 80 },
                  { field: 'quy_cach', headerName: 'Quy cách', width: 150 }
                ]}
                dialogTitle='Danh mục vật tư'
              />
            </div>
          </div>
          <div className='whitespace-nowrap'>
            <FormField name='trackInventory' type='checkbox' label='Chỉ xem vật tư có theo dõi tồn kho' />
          </div>
        </div>

        <div className='flex items-center pt-1'>
          <Label className='w-40 min-w-40'>Nhóm vật tư:</Label>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <div className='w-40'>
                <SearchField
                  type='text'
                  displayRelatedField='ten_phan_nhom'
                  columnDisplay='ten_phan_nhom'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=VT1`}
                  searchColumns={[
                    { field: 'ma_nhom', headerName: 'Mã nhóm', width: 120 },
                    { field: 'ten_phan_nhom', headerName: 'Tên nhóm', width: 200 }
                  ]}
                  dialogTitle='Danh mục nhóm vật tư 1'
                />
              </div>
              <div className='w-40'>
                <SearchField
                  type='text'
                  displayRelatedField='ten_phan_nhom'
                  columnDisplay='ten_phan_nhom'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=VT2`}
                  searchColumns={[
                    { field: 'ma_nhom', headerName: 'Mã nhóm', width: 120 },
                    { field: 'ten_phan_nhom', headerName: 'Tên nhóm', width: 200 }
                  ]}
                  dialogTitle='Danh mục nhóm vật tư 2'
                />
              </div>
              <div className='w-40'>
                <SearchField
                  type='text'
                  displayRelatedField='ten_phan_nhom'
                  columnDisplay='ten_phan_nhom'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=VT3`}
                  searchColumns={[
                    { field: 'ma_nhom', headerName: 'Mã nhóm', width: 120 },
                    { field: 'ten_phan_nhom', headerName: 'Tên nhóm', width: 200 }
                  ]}
                  dialogTitle='Danh mục nhóm vật tư 3'
                />
              </div>
            </div>
          </div>
        </div>
        <div className='flex items-center gap-1'>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <FormField
                name='balance'
                label='Số dư'
                labelClassName='w-40'
                type='select'
                options={[
                  { value: '0', label: '0. Tất cả' },
                  { value: '1', label: '1. Chỉ có hóa đơn số dư lớn hơn 0' },
                  { value: '2', label: '2. Chỉ những hóa đơn đã tất toán' }
                ]}
                className='w-[400px]'
                defaultValue={'0'}
              />
            </div>
          </div>
        </div>
        <div className='flex items-center gap-1'>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <FormField
                name='details_collection'
                label='Chi tiết thu tiền'
                labelClassName='w-40'
                type='select'
                options={[
                  { value: '0', label: '0. Không' },
                  { value: '1', label: '1. Có' }
                ]}
                className='w-[400px]'
                defaultValue={'1'}
              />
            </div>
          </div>
        </div>
        <div className='flex items-center gap-1'>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <FormField
                name='reportTemplate'
                label='Mẫu báo cáo'
                labelClassName='w-40'
                type='select'
                options={[
                  { value: 'TC', label: 'Mẫu tiền chuẩn' },
                  { value: 'NT', label: 'Mẫu ngoại tệ' }
                ]}
                className='w-[400px]'
                defaultValue={'TC'}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
