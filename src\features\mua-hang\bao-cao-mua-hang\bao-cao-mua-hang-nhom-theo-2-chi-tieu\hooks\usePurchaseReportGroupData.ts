import { useState, useCallback, useEffect } from 'react';
import {
  PurchaseReportGroupItem,
  PurchaseReportGroupResponse,
  SearchFormValues,
  UsePurchaseReportGroupReturn
} from '../types';
import api from '@/lib/api';

const generateMockData = (): PurchaseReportGroupItem[] => {
  return [
    {
      // System fields
      sysprint: '',
      stt: 1,
      sysorder: 1,
      syspivot: '',
      systotal: '',
      unit_id: 'CN001',
      id: '1',

      // Entity/Transaction fields
      ma_kh: 'NCC001',
      ma_vt: 'VT001',
      sl_nhap: 100,
      gia: 25000000,
      tien_nhap: 2500000000,
      cp: 50000000,
      thue: 250000000,

      // Grouping/Display fields
      ma: 'VT001',
      ten: 'Thép tấm A36 dày 10mm',
      nhom: 'THEP',
      ten_nhom: 'Nhóm thép'
    },
    {
      // System fields
      sysprint: '',
      stt: 2,
      sysorder: 2,
      syspivot: '',
      systotal: '',
      unit_id: 'CN001',
      id: '2',

      // Entity/Transaction fields
      ma_kh: 'NCC002',
      ma_vt: 'VT002',
      sl_nhap: 10000,
      gia: 500,
      tien_nhap: 5000000,
      cp: 100000,
      thue: 500000,

      // Grouping/Display fields
      ma: 'VT002',
      ten: 'Ốc vít M8x20',
      nhom: 'OC_VIT',
      ten_nhom: 'Nhóm ốc vít'
    },
    {
      // System fields
      sysprint: '',
      stt: 3,
      sysorder: 3,
      syspivot: '',
      systotal: '',
      unit_id: 'CN001',
      id: '3',

      // Entity/Transaction fields
      ma_kh: 'NCC003',
      ma_vt: 'VT003',
      sl_nhap: 1000,
      gia: 45000,
      tien_nhap: 45000000,
      cp: 900000,
      thue: 4500000,

      // Grouping/Display fields
      ma: 'VT003',
      ten: 'Dây điện đồng 2.5mm²',
      nhom: 'DIEN',
      ten_nhom: 'Nhóm điện'
    },
    {
      // System fields
      sysprint: '',
      stt: 4,
      sysorder: 4,
      syspivot: '',
      systotal: '',
      unit_id: 'CN002',
      id: '4',

      // Entity/Transaction fields
      ma_kh: 'NCC004',
      ma_vt: 'VT004',
      sl_nhap: 100,
      gia: 180000,
      tien_nhap: 18000000,
      cp: 360000,
      thue: 1800000,

      // Grouping/Display fields
      ma: 'VT004',
      ten: 'Sơn chống gỉ màu xám',
      nhom: 'SON',
      ten_nhom: 'Nhóm sơn'
    },
    {
      // System fields
      sysprint: '',
      stt: 5,
      sysorder: 5,
      syspivot: '',
      systotal: '',
      unit_id: 'CN002',
      id: '5',

      // Entity/Transaction fields
      ma_kh: 'NCC005',
      ma_vt: 'VT005',
      sl_nhap: 50,
      gia: 250000,
      tien_nhap: 12500000,
      cp: 250000,
      thue: 1250000,

      // Grouping/Display fields
      ma: 'VT005',
      ten: 'Bearing 6205-2RS',
      nhom: 'BEARING',
      ten_nhom: 'Nhóm bearing'
    },
    {
      // System fields
      sysprint: '',
      stt: 6,
      sysorder: 6,
      syspivot: '',
      systotal: '',
      unit_id: 'CN003',
      id: '6',

      // Entity/Transaction fields
      ma_kh: 'NCC006',
      ma_vt: 'VT006',
      sl_nhap: 200,
      gia: 85000,
      tien_nhap: 17000000,
      cp: 340000,
      thue: 1700000,

      // Grouping/Display fields
      ma: 'VT006',
      ten: 'Thép hộp 40x40x3',
      nhom: 'THEP',
      ten_nhom: 'Nhóm thép'
    },
    {
      // System fields
      sysprint: '',
      stt: 7,
      sysorder: 7,
      syspivot: '',
      systotal: '',
      unit_id: 'CN003',
      id: '7',

      // Entity/Transaction fields
      ma_kh: 'NCC007',
      ma_vt: 'VT007',
      sl_nhap: 500,
      gia: 120000,
      tien_nhap: 60000000,
      cp: 1200000,
      thue: 6000000,

      // Grouping/Display fields
      ma: 'VT007',
      ten: 'Xi măng Portland PCB40',
      nhom: 'XI_MANG',
      ten_nhom: 'Nhóm xi măng'
    },
    {
      // System fields
      sysprint: '',
      stt: 8,
      sysorder: 8,
      syspivot: '',
      systotal: '',
      unit_id: 'CN003',
      id: '8',

      // Entity/Transaction fields
      ma_kh: 'NCC008',
      ma_vt: 'VT008',
      sl_nhap: 2000,
      gia: 3500,
      tien_nhap: 7000000,
      cp: 140000,
      thue: 700000,

      // Grouping/Display fields
      ma: 'VT008',
      ten: 'Gạch ống 4 lỗ',
      nhom: 'GACH',
      ten_nhom: 'Nhóm gạch'
    }
  ];
};

/**
 * Custom hook for managing Purchase Report Group data
 *
 * This hook provides functionality to fetch purchase report group data
 * with mock support for testing and development purposes.
 */
export function usePurchaseReportGroupData(searchParams: SearchFormValues): UsePurchaseReportGroupReturn {
  const [data, setData] = useState<PurchaseReportGroupItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<PurchaseReportGroupResponse>(
        '/mua-hang/bao-cao-mua-hang/bao-cao-mua-hang-nhom-theo-2-chi-tieu/',
        {
          mock: true,
          mockData: mockData,
          params: searchParams
        }
      );

      const dataToSet = Array.isArray(response.data) ? response.data : response.data.results || [];
      setData(dataToSet);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
