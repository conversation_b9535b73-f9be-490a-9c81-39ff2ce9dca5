import { useState, useCallback } from 'react';
import {
  VatTaxDeclarationItem,
  VatTaxDeclarationResponse,
  ToKhaiThueGTGTSearchFormValues,
  UseVatTaxDeclarationReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): VatTaxDeclarationItem[] => {
  const vatItems = [
    { chi_tieu: '<PERSON><PERSON><PERSON> số hàng hóa, dịch vụ bán ra chịu thuế GTGT', ma_so: '01' },
    { chi_tieu: '<PERSON><PERSON><PERSON> số hàng hóa, dịch vụ bán ra không chịu thuế GTGT', ma_so: '02' },
    { chi_tieu: '<PERSON><PERSON><PERSON> số hàng hóa, dịch vụ bán ra được miễn thuế GTGT', ma_so: '03' },
    { chi_tieu: '<PERSON><PERSON><PERSON> số hàng hóa, dị<PERSON> v<PERSON> xu<PERSON> khẩ<PERSON>', ma_so: '04' },
    { chi_tieu: 'Thu<PERSON> GTGT của hàng hóa, dịch vụ bán ra', ma_so: '20' },
    { chi_tieu: 'Thuế GTGT của hàng hóa, dịch vụ mua vào được khấu trừ', ma_so: '21' },
    { chi_tieu: 'Thuế GTGT còn được khấu trừ kỳ trước chuyển sang', ma_so: '22' },
    { chi_tieu: 'Tổng số thuế GTGT được khấu trừ', ma_so: '23' },
    { chi_tieu: 'Số thuế GTGT phải nộp', ma_so: '24' },
    { chi_tieu: 'Số thuế GTGT còn được khấu trừ chuyển kỳ sau', ma_so: '25' }
  ];

  return vatItems.map((item, index) => ({
    id: (index + 1).toString(),
    stt_in: (index + 1).toString(),
    chi_tieu: item.chi_tieu,
    ma_so: item.ma_so,
    doanh_so: Math.floor(Math.random() * *********0) + *********,
    thue: Math.floor(Math.random() * *********) + 10000000
  }));
};

export function useVatTaxDeclaration(): UseVatTaxDeclarationReturn {
  const [data, setData] = useState<VatTaxDeclarationItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: ToKhaiThueGTGTSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<VatTaxDeclarationResponse>('/thue/to-khai/to-khai-thue-gtgt/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData({});
  }, [fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
