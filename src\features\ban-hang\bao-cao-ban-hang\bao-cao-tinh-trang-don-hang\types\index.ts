// TypeScript interfaces for Sales Order Status Report

export interface SalesOrderStatusItem {
  // System fields
  syspivot: string;
  sysorder: number;
  sysprint: string;
  systotal: string;

  // Core fields
  id: string;
  line: number;
  unit_id: string;

  // Entity codes
  ma_ngv: string;
  ma_kh: string;
  ma_nvbh: string;
  ma_ct: string;
  ma_vt: string;
  ma_bp: string;
  ma_cp0: number;
  ma_dtt: string;
  ma_hd: string;
  ma_ku: string;
  ma_lsx: string;
  ma_phi: number;
  ma_sp: string;
  ma_mr1: string;
  ma_mr2: string;
  ma_mr3: string;
  ma_vv: string;
  ma_unit: string;

  // Document fields
  ngay_ct: string;
  so_ct: string;
  status: string;
  dvt: string;
  he_so: number;
  ngay_giao: string;

  // Transaction fields
  gia2: number;
  tien2: number;
  so_luong: number;
  sl_xuat: number;
  sl_hd: number;
  sl_dh: number;
  sl_tl: number;
  sl_cl: number;

  // Display names
  ten_ngv: string;
  ten_kh: string;
  ten_vt: string;
  ten_ttct: string;
  ten_nvbh: string;

  // Optional summary flag
  isSummary?: boolean;
}

export interface SalesOrderStatusResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: SalesOrderStatusItem[];
}

export interface SearchFormValues {
  ngay_tu?: string;
  ngay_den?: string;
  so_chung_tu_tu?: string;
  so_chung_tu_den?: string;
  ma_khach_hang?: string;
  nhom_khach_hang?: string[];
  ma_vat_tu?: string;
  loai_vat_tu?: string;
  nhom_vat_tu?: string[];
  ma_kho?: string;
  han_giao_hang?: string;
  trang_thai?: string;
  mau_bao_cao?: string;
  loai_don_hang?: string;
  ma_giao_dich?: string;
  ma_lo?: string;
  ma_vi_tri?: string;
  dien_giai?: string;
  mau_loc_bao_cao?: string;
  [key: string]: any;
}

export interface UseSalesOrderStatusReturn {
  data: SalesOrderStatusItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
