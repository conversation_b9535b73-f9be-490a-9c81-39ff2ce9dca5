'use client';

import { useState } from 'react';
import { AritoDialog, AritoForm, BottomBar, AritoIcon } from '@/components/custom/arito';
import { SearchFormValues, SearchFormSchema } from '../../schema';
import { AccountModel, DoiTuong } from '@/types/schemas';
import { BasicInfoTab } from './BasicInfoTab';
import { FormMode } from '@/types/form';
import { DetailTab } from './DetailTab';
import { OtherTab } from './OtherTab';

interface SearchDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: SearchFormValues;
  onSubmit?: (data: SearchFormValues) => void;
  onClose: () => void;
}

const SearchDialog = ({ open, onClose, onSubmit, formMode, initialData }: SearchDialogProps) => {
  const [taiKhoan, setTaiKhoan] = useState<AccountModel | null>(null);
  const [nhaCungCap, setNhaCungCap] = useState<DoiTuong | null>(null);

  const handleSubmit = (data: SearchFormValues) => {
    onSubmit?.(data);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={'Báo cáo đối chiếu công nợ'}
      maxWidth='xl'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode={formMode}
        hasAritoActionBar={false}
        schema={SearchFormSchema}
        onSubmit={handleSubmit}
        initialData={initialData}
        className='w-[900px]'
        headerFields={<BasicInfoTab formMode={formMode} taiKhoan={taiKhoan} setTaiKhoan={setTaiKhoan} />}
        tabs={[
          {
            id: 'detail',
            label: 'Chi tiết',
            component: <DetailTab formMode={formMode} nhaCungCap={nhaCungCap} setNhaCungCap={setNhaCungCap} />
          },
          {
            id: 'other',
            label: 'Khác',
            component: <OtherTab formMode={formMode} />
          }
        ]}
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={<BottomBar mode={formMode} onClose={onClose} />}
      />
    </AritoDialog>
  );
};

export default SearchDialog;
