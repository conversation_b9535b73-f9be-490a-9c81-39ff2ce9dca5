/**
 * TypeScript interface for CongCuDungCu (Tool Equipment) model
 *
 * This interface represents the structure of the CongCuDungCu model from the backend.
 * It defines tool and equipment entities in the system.
 */

import { ApiResponse } from '../api.type';

export interface CongCuDungCu {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model?: string;

  /**
   * Tool code
   */
  ma_cc: string;

  /**
   * Tool name
   */
  ten_cc: string;

  /**
   * First allocation date
   */
  ngay_pb1: string;

  /**
   * Status indicator (default='active')
   */
  status: string;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Type for CongCuDungCu API response
 */
export type CongCuDungCuResponse = ApiResponse<CongCuDungCu>;
