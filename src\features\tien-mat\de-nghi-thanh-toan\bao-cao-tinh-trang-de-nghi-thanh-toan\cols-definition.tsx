import { GridColDef } from '@mui/x-data-grid';

/**
 * Status options for payment request status report
 */
export const STATUS_OPTIONS = [
  { label: 'Tất cả', value: '10' },
  { label: 'Lậ<PERSON> chứng từ', value: '0' },
  { label: 'Chờ duyệt', value: '1' },
  { label: 'Đang duyệt', value: '4' },
  { label: 'Đã duyệt', value: '5' },
  { label: '<PERSON>ang thực hiện', value: '6' },
  { label: 'Hoàn thành', value: '7' },
  { label: 'Đóng', value: '5' }
];

/**
 * Get status label by value
 */
export const getStatusLabel = (value: string): string => {
  const status = STATUS_OPTIONS.find(option => option.value === value);
  return status ? status.label : value;
};

export const getDataTableColumns: GridColDef[] = [
  {
    field: 'unit_id',
    headerName: 'Đơn vị',
    width: 100
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 120
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 120
  },
  {
    field: 'ma_bp',
    headerName: 'Mã bộ phận',
    width: 120
  },
  {
    field: 'ten_bp',
    headerName: 'Tên bộ phận',
    width: 250
  },
  {
    field: 'nguoi_yc',
    headerName: 'Người yêu cầu',
    width: 150
  },
  {
    field: 'dien_giai',
    headerName: 'Lý do chi',
    width: 250
  },
  {
    field: 'tien',
    headerName: 'Số tiền đề nghị',
    width: 150,
    type: 'number',
    valueFormatter: (params: any) => params.value?.toLocaleString('vi-VN')
  },
  {
    field: 'tien_chi',
    headerName: 'Số tiền đã chi',
    width: 150,
    type: 'number',
    valueFormatter: (params: any) => params.value?.toLocaleString('vi-VN')
  },
  {
    field: 'tien_cl',
    headerName: 'Số tiền còn lại',
    width: 150,
    type: 'number',
    valueFormatter: (params: any) => params.value?.toLocaleString('vi-VN')
  },
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120,
    renderCell: (params: any) => getStatusLabel(params.value || '10')
  }
];
