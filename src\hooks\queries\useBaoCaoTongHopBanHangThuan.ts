import { useState, useCallback } from 'react';
import {
  BaoCaoTongHopBanHangThuanItem,
  BaoCaoTongHopBanHangThuanResponse,
  BaoCaoTongHopBanHangThuanSearchFormValues,
  UseBaoCaoTongHopBanHangThuanReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): BaoCaoTongHopBanHangThuanItem[] => {
  return [
    {
      id: '1',
      ma_vt: 'A1010010001',
      ten_vt: '<PERSON><PERSON> (S)',
      dvt: 'Ly',
      tien_ban: 150000,
      tien_tl: 0,
      tien_cl: 150000
    },
    {
      id: '2',
      ma_vt: 'A1010010002',
      ten_vt: '<PERSON><PERSON> (M)',
      dvt: 'Ly',
      tien_ban: 0,
      tien_tl: 180000,
      tien_cl: -180000
    },
    {
      id: '3',
      ma_vt: 'A1010010004',
      ten_vt: '<PERSON><PERSON> (M)',
      dvt: 'Ly',
      tien_ban: 200000,
      tien_tl: 25000,
      tien_cl: 175000
    },
    {
      id: '4',
      ma_vt: 'B2020020001',
      ten_vt: 'Bánh Mì Thịt Nướng',
      dvt: 'Cái',
      tien_ban: 350000,
      tien_tl: 0,
      tien_cl: 350000
    },
    {
      id: '5',
      ma_vt: 'C3030030001',
      ten_vt: 'Nước Cam Ép',
      dvt: 'Ly',
      tien_ban: 120000,
      tien_tl: 15000,
      tien_cl: 105000
    }
  ];
};

/**
 * Custom hook for managing BaoCaoTongHopBanHangThuan (Sales Summary Report) data
 *
 * This hook provides functionality to fetch sales summary report data
 * with mock support for testing and development purposes.
 */
export function useBaoCaoTongHopBanHangThuan(
  searchParams: BaoCaoTongHopBanHangThuanSearchFormValues
): UseBaoCaoTongHopBanHangThuanReturn {
  const [data, setData] = useState<BaoCaoTongHopBanHangThuanItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BaoCaoTongHopBanHangThuanSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BaoCaoTongHopBanHangThuanResponse>(
        '/ban-hang/bao-cao-tong-hop-ban-hang-thuan/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
