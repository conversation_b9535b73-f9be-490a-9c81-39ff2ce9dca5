import { GridColDef } from '@mui/x-data-grid';

export const getDocumentsListColumns: GridColDef[] = [
  {
    field: 'ma_unit',
    headerName: 'Đơn vị',
    width: 120
  },
  {
    field: 'ngay_ct',
    headerName: '<PERSON><PERSON>y c/từ',
    width: 120,
    cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold text-center' : ''),
    renderCell: params => {
      return params.row.ngay_ct;
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 120
  },
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 130
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 180,
    cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
  },
  {
    field: 'dien_giai',
    headerName: '<PERSON><PERSON><PERSON> g<PERSON>',
    width: 200
  },
  {
    field: 'tk',
    headerName: 'Tài khoản',
    width: 120
  },
  {
    field: 'tk_du',
    headerName: 'Tk đối ứng',
    width: 120
  },
  {
    field: 'ps_no',
    headerName: 'Ps nợ',
    width: 120,
    type: 'number',
    cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : ''),
    renderCell: (params: any) => {
      return params.row.ps_no > 0 ? params.row.ps_no.toLocaleString('vi-VN') : '';
    }
  },
  {
    field: 'ps_co',
    headerName: 'Ps có',
    width: 120,
    type: 'number',
    cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : ''),
    renderCell: (params: any) => {
      return params.row.ps_co > 0 ? params.row.ps_co.toLocaleString('vi-VN') : '';
    }
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 130
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 100
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 130
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 100
  },
  {
    field: 'ma_ct',
    headerName: 'Mã c/từ',
    width: 120
  }
];
