/**
 * TypeScript interfaces for BangTinhKhauHaoTSCDTT (Monthly Fixed Asset Depreciation Calculation Report)
 *
 * This interface represents the structure of the BangTinhKhauHaoTSCDTT model from the backend.
 * It defines detailed information about monthly fixed asset depreciation calculations.
 */

import { ApiResponse } from '../api.type';

export interface BangTinhKhauHaoTSCDTTItem {
  /**
   * Unique identifier
   */
  id: string;

  /**
   * Asset code
   */
  ma_ts: string;

  /**
   * Asset name
   */
  ten_ts: string;

  /**
   * Unit name
   */
  ten_dvt: string;

  /**
   * Quantity
   */
  so_luong: number;

  /**
   * Asset type code
   */
  ma_lts: string;

  /**
   * Asset type name
   */
  ten_lts: string;

  /**
   * Initial depreciation date
   */
  ngay_kh0: string;

  /**
   * Number of depreciation periods
   */
  so_ky_kh: number;

  /**
   * Depreciation end date
   */
  ngay_kh_kt: string;

  /**
   * Asset account
   */
  tk_ts: string;

  /**
   * Department code
   */
  ma_bp: string;

  /**
   * System total
   */
  systotal: number;
}

/**
 * API response type for BangTinhKhauHaoTSCDTT
 */
export interface BangTinhKhauHaoTSCDTTResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BangTinhKhauHaoTSCDTTItem[];
}

/**
 * Type for BangTinhKhauHaoTSCDTT API response
 */
export type BangTinhKhauHaoTSCDTTApiResponse = ApiResponse<BangTinhKhauHaoTSCDTTItem>;

/**
 * Hook return type for useBangTinhKhauHaoTSCDTT
 */
export interface UseBangTinhKhauHaoTSCDTTReturn {
  data: BangTinhKhauHaoTSCDTTItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: any) => Promise<void>;
  refreshData: () => Promise<void>;
}
