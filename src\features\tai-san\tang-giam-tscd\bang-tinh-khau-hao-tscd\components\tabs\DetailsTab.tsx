import { boPhanSearchColumns, groupColumns, loaiTaiSanSearchColumns } from '@/constants/search-columns';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import type { <PERSON><PERSON><PERSON>, LoaiTSCDCCDC } from '@/types/schemas';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

interface DetailsTabProps {
  searchFieldStates: {
    loaiTaiSan: LoaiTSCDCCDC | null;
    setLoaiTaiSan: (loaiTaiSan: LoaiTSCDCCDC | null) => void;
    boPhan: BoPhan | null;
    setBoPhan: (boPhan: BoPhan | null) => void;
    nhom1: any | null;
    setNhom1: (nhom1: any | null) => void;
    nhom2: any | null;
    setNhom2: (nhom2: any | null) => void;
    nhom3: any | null;
    setNhom3: (nhom3: any | null) => void;
  };
}

const DetailsTab: React.FC<DetailsTabProps> = ({ searchFieldStates }) => {
  const { loaiTaiSan, setLoaiTaiSan, boPhan, setBoPhan, nhom1, setNhom1, nhom2, setNhom2, nhom3, setNhom3 } =
    searchFieldStates;
  return (
    <div className='min-w-[850px] space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* Loại tài sản */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Loại tài sản</Label>
          <SearchField<LoaiTSCDCCDC>
            searchEndpoint={`/${QUERY_KEYS.LOAI_TAI_SAN_CONG_CU}/`}
            searchColumns={loaiTaiSanSearchColumns}
            dialogTitle='Danh mục loại tài sản'
            columnDisplay='ma_lts'
            displayRelatedField='ten_lts'
            value={loaiTaiSan?.ma_lts || ''}
            onRowSelection={setLoaiTaiSan}
          />
        </div>

        {/* Bộ phận sử dụng */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Bộ phận sử dụng</Label>
          <SearchField<BoPhan>
            searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
            searchColumns={boPhanSearchColumns}
            dialogTitle='Danh mục bộ phận'
            columnDisplay='ma_bp'
            displayRelatedField='ten_bp'
            value={boPhan?.ma_bp || ''}
            onRowSelection={setBoPhan}
          />
        </div>

        {/* Nhóm tài sản 1,2,3 */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Nhóm tài sản 1,2,3</Label>
          <div className='flex gap-1'>
            <SearchField
              searchEndpoint={`/${QUERY_KEYS.NHOM_TSCD}/`}
              searchColumns={groupColumns}
              dialogTitle='Danh mục nhóm tài sản 1'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              value={nhom1?.ma_nhom || ''}
              onRowSelection={setNhom1}
            />
            <SearchField
              searchEndpoint={`/${QUERY_KEYS.NHOM_TSCD}/`}
              searchColumns={groupColumns}
              dialogTitle='Danh mục nhóm tài sản 2'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              value={nhom2?.ma_nhom || ''}
              onRowSelection={setNhom2}
            />
            <SearchField
              searchEndpoint={`/${QUERY_KEYS.NHOM_TSCD}/`}
              searchColumns={groupColumns}
              dialogTitle='Danh mục nhóm tài sản 3'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              value={nhom3?.ma_nhom || ''}
              onRowSelection={setNhom3}
            />
          </div>
        </div>

        {/* Người lập */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Người lập</Label>
          <FormField name='nguoi_lap' type='text' className='w-96' />
        </div>

        {/* Mẫu báo cáo */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Mẫu báo cáo</Label>
          <FormField
            name='mau_bc'
            type='select'
            options={[
              { value: 'TC', label: 'Mẫu tiêu chuẩn' },
              { value: 'NT', label: 'Mẫu ngoại tệ' }
            ]}
            className='w-60'
          />
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
