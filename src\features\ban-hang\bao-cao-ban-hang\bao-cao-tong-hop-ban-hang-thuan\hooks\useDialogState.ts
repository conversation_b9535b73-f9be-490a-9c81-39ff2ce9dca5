import { useState } from 'react';

export interface SearchParams {
  [key: string]: any;
}

export interface PrintTemplateData {
  [key: string]: any;
}

export interface UseDialogStateReturn {
  initialSearchDialogOpen: boolean;
  editPrintTemplateDialogOpen: boolean;
  showTable: boolean;
  handleInitialSearchClose: () => void;
  handleInitialSearch: (values: SearchParams) => void;
  handleSearchClick: () => void;
  handleEditPrintTemplateClick: () => void;
  handleClosePrintTemplateDialog: () => void;
  handleSavePrintTemplate: (data: PrintTemplateData) => void;
  setShowTable: (show: boolean) => void;
}

export function useDialogState(): UseDialogStateReturn {
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [editPrintTemplateDialogOpen, setEditPrintTemplateDialogOpen] = useState(false);
  const [showTable, setShowTable] = useState(false);

  const handleInitialSearchClose = () => {
    setInitialSearchDialogOpen(false);
  };

  const handleInitialSearch = (values: SearchParams) => {
    // Just show the table and close the dialog without setting searchParams
    setShowTable(true);
    setInitialSearchDialogOpen(false);
  };

  const handleSearchClick = () => {
    setInitialSearchDialogOpen(true);
  };

  const handleEditPrintTemplateClick = () => {
    setEditPrintTemplateDialogOpen(true);
  };

  const handleClosePrintTemplateDialog = () => {
    setEditPrintTemplateDialogOpen(false);
  };

  const handleSavePrintTemplate = (data: PrintTemplateData) => {
    console.log('Print template saved:', data);
    setEditPrintTemplateDialogOpen(false);
  };

  return {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate,
    setShowTable
  };
}
