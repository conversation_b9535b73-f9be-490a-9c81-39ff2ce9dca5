// TypeScript interfaces for Purchase Report Group

export interface PurchaseReportGroupItem {
  // System fields
  sysprint?: string;
  stt?: number;
  sysorder?: number;
  syspivot?: string;
  systotal?: string;
  unit_id?: string;
  id: string;

  // Entity/Transaction fields mapped to existing columns
  ma_kh?: string; // Customer/supplier code
  ma_vt: string; // Material code -> materialCode
  sl_nhap: number; // Quantity imported -> quantity
  gia: number; // Price -> unitPrice
  tien_nhap: number; // Import amount -> totalAmount
  cp: number; // Cost -> costAmount
  thue: number; // Tax -> taxAmount

  // Grouping/Display fields
  ma?: string; // Code
  ten: string; // Name -> materialName
  nhom?: string; // Group code
  ten_nhom?: string; // Group name
}

export interface PurchaseReportGroupResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: PurchaseReportGroupItem[];
}

export interface SearchFormValues {
  ngay_tu?: string;
  ngay_den?: string;
  so_chung_tu_tu?: string;
  so_chung_tu_den?: string;
  ma_nha_cung_cap?: string;
  nhom_nha_cung_cap?: string[];
  ma_vat_tu?: string;
  loai_vat_tu?: string;
  nhom_vat_tu?: string[];
  ma_kho?: string;
  chi_tieu_1?: string;
  chi_tieu_2?: string;
  nhom_theo_chi_tieu_1?: string;
  nhom_theo_chi_tieu_2?: string;
  mau_bao_cao?: string;
  loai_don_hang?: string;
  ma_giao_dich?: string;
  ma_lo?: string;
  ma_vi_tri?: string;
  dien_giai?: string;
  mau_loc_bao_cao?: string;
  [key: string]: any;
}

export interface UsePurchaseReportGroupReturn {
  data: PurchaseReportGroupItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
