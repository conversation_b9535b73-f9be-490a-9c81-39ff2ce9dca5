import { FormField } from '@/components/custom/arito/form/form-field';

export const DetailTab = () => {
  return (
    <div className='p-4'>
      <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
        <div className='space-y-2'>
          {/* Classification Select Field */}
          <FormField
            className='grid w-[62%] grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='select'
            label='Phân loại'
            name='phan_loai'
            options={[
              { value: 'chi_tiet', label: 'Chi tiết' },
              { value: 'nhom_theo_tk_doi_ung', label: 'Nhóm theo tài khoản đối ứng' },
              { value: 'nhom_theo_chung_tu', label: 'Nhóm theo chứng từ' }
            ]}
            inputClassName='160px'
          />

          {/* Report Template Select Field */}
          <FormField
            className='grid w-3/4 grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='select'
            label='Mẫu báo cáo'
            name='mau_bao_cao'
            options={[
              { value: 'standard', label: 'Mẫu tiền chuẩn' },
              { value: 'foreign_currency', label: 'Mẫu ngoại tệ' }
            ]}
            inputClassName='160px'
          />
        </div>
      </div>
    </div>
  );
};

export default DetailTab;
