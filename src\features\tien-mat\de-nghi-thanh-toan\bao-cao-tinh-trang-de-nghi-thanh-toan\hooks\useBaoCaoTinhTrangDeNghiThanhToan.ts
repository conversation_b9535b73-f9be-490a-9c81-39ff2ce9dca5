import { useState, useCallback } from 'react';
import {
  BaoCaoTinhTrangDeNghiThanhToanItem,
  BaoCaoTinhTrangDeNghiThanhToanResponse,
  UseBaoCaoTinhTrangDeNghiThanhToanReturn
} from '@/types/schemas/bao-cao-tinh-trang-de-nghi-thanh-toan.type';
import { STATUS_OPTIONS } from '../cols-definition';
import { SearchFormValues } from '../schema';
import api from '@/lib/api';

const generateMockData = (): BaoCaoTinhTrangDeNghiThanhToanItem[] => {
  // Use status values instead of labels for proper data binding
  const statusValues = STATUS_OPTIONS.filter(option => option.value !== '10').map(option => option.value);
  const unitOptions = ['CN_HN', 'CN_HCM', 'CN_DN', 'CN_CT'];
  const departmentCodes = ['BP001', 'BP002', 'BP003', 'BP004', 'BP005'];
  const departmentNames = ['Phòng Kế toán', 'Phòng Nhân sự', 'Phòng Kinh doanh', 'Phòng Kỹ thuật', 'Phòng Hành chính'];
  const documentTypes = ['DNTT', 'PHTT', 'YCTT', 'DXTT'];
  const documentTypeNames = ['Đề nghị thanh toán', 'Phiếu thanh toán', 'Yêu cầu thanh toán', 'Đề xuất thanh toán'];
  const userIds = ['USER001', 'USER002', 'USER003', 'USER004', 'USER005'];
  const requesters = ['Nguyễn Văn A', 'Trần Thị B', 'Lê Văn C', 'Phạm Thị D', 'Hoàng Văn E'];

  return Array.from({ length: 15 }, (_, index) => {
    const baseAmount = 10000000 + index * 2000000;
    const paidAmount = Math.floor(baseAmount * (0.3 + Math.random() * 0.4));
    const remainingAmount = baseAmount - paidAmount;

    return {
      id: (index + 1).toString(),
      unit_id: unitOptions[index % unitOptions.length],
      ngay_ct: new Date(2024, 0, 15 + index).toISOString().split('T')[0],
      so_ct: `DNTT2024${String(index + 1).padStart(3, '0')}`,
      ma_ct: documentTypes[index % documentTypes.length],
      ma_bp: departmentCodes[index % departmentCodes.length],
      user_id: userIds[index % userIds.length],
      dien_giai: `Đề nghị thanh toán ${index % 2 === 0 ? 'chi phí hoạt động' : 'mua sắm thiết bị'} tháng ${(index % 12) + 1}/2024`,
      tien: baseAmount,
      tien_chi: paidAmount,
      status: statusValues[index % statusValues.length],
      tien_cl: remainingAmount,
      ten_bp: departmentNames[index % departmentNames.length],
      ten_ttct: documentTypeNames[index % documentTypeNames.length],
      ma_unit: unitOptions[index % unitOptions.length],
      nguoi_yc: requesters[index % requesters.length]
    };
  });
};

/**
 * Custom hook for managing BaoCaoTinhTrangDeNghiThanhToan (Payment Request Status Report) data
 *
 * This hook provides functionality to fetch payment request status report data
 * with mock support for testing and development purposes.
 */
export function useBaoCaoTinhTrangDeNghiThanhToan(
  searchParams: SearchFormValues
): UseBaoCaoTinhTrangDeNghiThanhToanReturn {
  const [data, setData] = useState<BaoCaoTinhTrangDeNghiThanhToanItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BaoCaoTinhTrangDeNghiThanhToanResponse>(
        '/tien-mat/de-nghi-thanh-toan/bao-cao-tinh-trang-de-nghi-thanh-toan/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
