/**
 * Utility functions for formatting numbers, currency values, and dates
 */

/**
 * Format a number as currency in Vietnamese format
 * @param value - The number to format
 * @returns Formatted currency string
 */
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(value);
}

/**
 * Format a number in Vietnamese format
 * @param value - The number to format
 * @returns Formatted number string
 */
export function formatNumber(value: number): string {
  return new Intl.NumberFormat('vi-VN').format(value);
}

/**
 * Format a number as currency in Vietnamese format and replace the currency symbol with 'VND'
 * @param value - The number to format
 * @returns Formatted currency string with 'VND' instead of '₫'
 */
export function formatCurrencyWithVND(value: number): string {
  return formatCurrency(value).replace('₫', 'VND');
}

/**
 * Format a date to dd/mm/yyyy format
 * @param date - The date to format (can be Date object, string, or null/undefined)
 * @returns Formatted date string in dd/mm/yyyy format, or empty string if invalid
 */
export function formatDate(date: Date | string | null | undefined): string {
  if (!date) return '';

  try {
    let dateObj: Date;

    if (typeof date === 'string') {
      // Handle various string formats
      if (date.includes('-')) {
        // Handle ISO format (yyyy-mm-dd) or other dash-separated formats
        dateObj = new Date(date);
      } else if (date.includes('/')) {
        // Handle slash-separated formats
        dateObj = new Date(date);
      } else {
        // Try to parse as-is
        dateObj = new Date(date);
      }
    } else {
      dateObj = date;
    }

    // Check if the date is valid
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    const day = dateObj.getDate().toString().padStart(2, '0');
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const year = dateObj.getFullYear();

    return `${day}-${month}-${year}`;
  } catch (error) {
    console.warn('Error formatting date:', error);
    return '';
  }
}
