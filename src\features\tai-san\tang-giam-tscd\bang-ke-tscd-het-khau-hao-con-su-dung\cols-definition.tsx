import { GridColDef } from '@mui/x-data-grid';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';
export const fullyDepreciatedAssetsColumns: GridColDef[] = [
  {
    field: 'stt',
    headerName: 'STT',
    width: 70
  },
  {
    field: 'ma_tai_san',
    headerName: 'Mã tài sản',
    width: 130
  },
  {
    field: 'ten_tai_san',
    headerName: 'Tên tài sản',
    width: 200
  },
  {
    field: 'nguyen_gia',
    headerName: 'Nguyên giá',
    width: 130,
    type: 'number'
  },
  {
    field: 'gt_da_khau_hao',
    headerName: 'Gt đã khấu hao',
    width: 150,
    type: 'number'
  },
  {
    field: 'ngay_mua',
    headerName: 'Ngày mua',
    width: 120,
    type: 'date',
    renderCell: (params: any) => {
      return params.value ? new Date(params.value).toLocaleDateString('vi-VN') : '';
    }
  },
  {
    field: 'ngay_tinh_kh',
    headerName: 'Ngày tính kh',
    width: 120,
    type: 'date',
    renderCell: (params: any) => {
      return params.value ? new Date(params.value).toLocaleDateString('vi-VN') : '';
    }
  },
  {
    field: 'so_ky_khau_hao',
    headerName: 'Số kỳ khấu hao',
    width: 140,
    type: 'number'
  },
  {
    field: 'ngay_kt_kh',
    headerName: 'Ngày kt kh',
    width: 120,
    type: 'date',
    renderCell: (params: any) => {
      return params.value ? new Date(params.value).toLocaleDateString('vi-VN') : '';
    }
  }
];

// Định nghĩa các cột phân tích
export const columnAnalysisItems = [
  { label: 'Stt', type: 'string' },
  { label: 'Ngày c/từ', type: 'string' },
  { label: 'Số c/từ', type: 'array' },
  { label: 'Ngày hóa đơn', type: 'string' },
  { label: 'Số hóa đơn', type: 'array' },
  { label: 'Mã khách hàng', type: 'array' },
  { label: 'Tên khách hàng', type: 'array' },
  { label: 'Mã NVBH', type: 'array' },
  { label: 'Tên NVBH', type: 'array' },
  { label: 'Tổng tiền HĐ nt', type: 'number' },
  { label: 'Đã thu nt', type: 'number' },
  { label: 'Phải thu nt', type: 'number' },
  { label: 'Tổng tiền HĐ', type: 'number' },
  { label: 'Đã thu', type: 'number' },
  { label: 'Phải thu', type: 'number' },
  { label: 'Trong hạn', type: 'number' },
  { label: 'Quá hạn %s1 ngày', type: 'number' },
  { label: 'Quá hạn %s2 ngày', type: 'number' },
  { label: 'Quá hạn %s3 ngày', type: 'number' },
  { label: 'Quá hạn %s4 ngày', type: 'number' },
  { label: 'Quá hạn trên %s5 ngày', type: 'number' },
  { label: 'Ngày đến hạn', type: 'string' },
  { label: 'Hạn tt', type: 'string' },
  { label: 'Số ngày đến hạn', type: 'number' },
  { label: 'Diễn giải', type: 'string' },
  { label: 'ID', type: 'string' },
  { label: 'Mã chứng từ', type: 'array' },
  { label: 'Đơn vị', type: 'array' }
];
