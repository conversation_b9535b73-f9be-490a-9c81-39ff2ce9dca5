import { z } from 'zod';

export const searchSchema = z.object({
  tu_ky: z.number().optional(),
  tu_nam: z.number().optional(),
  den_ky: z.number().optional(),
  den_nam: z.number().optional(),
  ma_cc: z.string().optional(),
  ma_lcc: z.string().optional(),
  ma_bp: z.string().optional(),
  nh_cc1: z.string().optional(),
  nh_cc2: z.string().optional(),
  nh_cc3: z.string().optional(),
  detailBy: z.string().optional(),
  ma_unit: z.string().optional(),
  mau_bc: z.coerce.number().optional(),
  report_filtering: z.string().optional(),
  data_analysis_struct: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  tu_ky: new Date().getMonth() + 1,
  tu_nam: new Date().getFullYear(),
  den_ky: new Date().getMonth() + 1,
  den_nam: new Date().getFullYear(),
  ma_cc: '',
  ma_lcc: '',
  ma_bp: '',
  nh_cc1: '',
  nh_cc2: '',
  nh_cc3: '',
  detailBy: '1',
  ma_unit: '',
  mau_bc: 20,
  report_filtering: '1',
  data_analysis_struct: '1'
};
