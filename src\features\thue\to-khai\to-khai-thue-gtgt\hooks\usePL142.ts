import { useState, useCallback } from 'react';
import { PL142Item, PL142Response, ToKhaiThueGTGTSearchFormValues, UsePL142Return } from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): PL142Item[] => {
  const services = [
    'Dịch vụ tư vấn công nghệ thông tin',
    'Dịch vụ phát triển phần mềm',
    'Dịch vụ bảo trì hệ thống',
    'Dịch vụ đào tạo nhân viên',
    'D<PERSON>ch vụ thiết kế website',
    'Dịch vụ quản lý dữ liệu',
    'D<PERSON><PERSON> vụ an ninh mạng',
    'Dịch vụ cloud computing'
  ];

  return services.map((service, index) => ({
    id: (index + 1).toString(),
    ten_hang_hoa_dich_vu: service,
    ma_so: `DV${String(index + 1).padStart(3, '0')}`,
    tien_chua_thue_nt: Math.floor(Math.random() * 50000000) + 10000000,
    tien_chua_thue: Math.floor(Math.random() * 50000000) + 10000000,
    thue_suat_quy_dinh: [0, 5, 10][Math.floor(Math.random() * 3)],
    thue_suat_sau_giam: [0, 3, 8][Math.floor(Math.random() * 3)],
    thue_duoc_giam_nt: Math.floor(Math.random() * 2000000) + 500000,
    thue_duoc_giam: Math.floor(Math.random() * 2000000) + 500000,
    thue_gtgt_cua_hh_dv_nt: Math.floor(Math.random() * 5000000) + 1000000,
    thue_gtgt_cua_hh_dv: Math.floor(Math.random() * 5000000) + 1000000
  }));
};

export function usePL142(): UsePL142Return {
  const [data, setData] = useState<PL142Item[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: ToKhaiThueGTGTSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<PL142Response>('/thue/to-khai/to-khai-thue-gtgt/pl142/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData({});
  }, [fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
