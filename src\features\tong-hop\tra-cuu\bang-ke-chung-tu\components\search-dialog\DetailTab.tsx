import {
  khachHangSearchColumns,
  QUERY_KEYS,
  accountListSearchColumns,
  ngoaiTeSearchColumns,
  chungTu1SearchColumns
} from '@/constants';
import { SearchDialogState, SearchDialogActions } from '../../hooks/useSearchDialogState';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { AccountModel, ChungTu, KhachHang, NgoaiTe } from '@/types/schemas';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface DetailTabProps {
  formMode: FormMode;
  searchState: {
    state: SearchDialogState;
    actions: SearchDialogActions;
  };
}

export const DetailTab: React.FC<DetailTabProps> = ({ formMode, searchState: { state, actions } }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài kho<PERSON>n</Label>
          <SearchField<AccountModel>
            type='text'
            name='tk'
            searchColumns={accountListSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            classNameRelatedField='w-full'
            onRowSelection={actions.setTaiKhoan}
            value={state.taiKhoan?.code || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ghi nợ/có</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='no_co'
              disabled={formMode === 'view'}
              options={[
                { label: 'Tất cả', value: 'all' },
                { label: 'Nợ', value: 'ps_no' },
                { label: 'Có', value: 'ps_co' }
              ]}
              defaultValue={'0'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản đối ứng</Label>
          <SearchField<AccountModel>
            type='text'
            name='tk_du'
            searchColumns={accountListSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            classNameRelatedField='w-full'
            onRowSelection={actions.setTaiKhoanDu}
            value={state.taiKhoanDu?.code || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã đối tượng</Label>
          <SearchField<KhachHang>
            type='text'
            name='ma_kh'
            searchColumns={khachHangSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
            dialogTitle='Danh mục đối tượng'
            columnDisplay='customer_code'
            displayRelatedField='customer_name'
            classNameRelatedField='w-full'
            onRowSelection={actions.setKhachHang}
            value={state.khachHang?.customer_code || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngoại tệ</Label>
          <SearchField<NgoaiTe>
            type='text'
            name='ma_nt'
            searchColumns={ngoaiTeSearchColumns}
            searchEndpoint={`/${QUERY_KEYS.NGOAI_TE}/`}
            dialogTitle='Danh mục ngoại tệ'
            columnDisplay='ma_nt'
            displayRelatedField='ten_nt'
            classNameRelatedField='w-full'
            onRowSelection={actions.setNgoaiTe}
            value={state.ngoaiTe?.ma_nt || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã chứng từ</Label>
          <SearchField<ChungTu>
            type='text'
            name='ma_ct'
            searchColumns={chungTu1SearchColumns}
            searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}/`}
            dialogTitle='Danh mục chứng từ'
            columnDisplay='ma_ct'
            displayRelatedField='ten_ct'
            classNameRelatedField='w-full'
            onRowSelection={actions.setChungTu}
            value={state.chungTu?.ma_ct || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Diễn giải</Label>
          <div className='w-[350px]'>
            <FormField type='text' name='dien_giai' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='mau_bc'
              disabled={formMode === 'view'}
              options={[
                { label: 'Mẫu tiền chuẩn', value: '20' },
                { label: 'Mẫu ngoại tệ', value: '30' }
              ]}
              defaultValue={'20'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
