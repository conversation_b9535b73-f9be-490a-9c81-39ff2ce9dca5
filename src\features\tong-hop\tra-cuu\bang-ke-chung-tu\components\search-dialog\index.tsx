'use client';

import { AritoDialog, AritoForm, BottomBar, AritoIcon } from '@/components/custom/arito';
import { SearchFormValues, SearchFormSchema } from '../../schema';
import { useSearchDialogState } from '../../hooks';
import { BasicInfoTab } from './BasicInfoTab';
import { FormMode } from '@/types/form';
import { DetailTab } from './DetailTab';
import { FilterTab } from './FilterTab';
import { OtherTab } from './OtherTab';

interface SearchDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: SearchFormValues;
  onSubmit?: (data: SearchFormValues) => void;
  onClose: () => void;
}

const SearchDialog = ({ open, onClose, onSubmit, formMode, initialData }: SearchDialogProps) => {
  const { state, actions } = useSearchDialogState();

  const handleSubmit = (data: SearchFormValues) => {
    onSubmit?.({
      ...data,
      tk: state.taiKhoan?.uuid || '',
      tk_du: state.taiKhoanDu?.uuid || '',
      ma_kh: state.khachHang?.uuid || '',
      ma_nt: state.ngoaiTe?.uuid || '',
      ma_ct: state.chungTu?.uuid || '',
      ma_bp: state.boPhan?.uuid || '',
      ma_vv: state.vuViec?.uuid || '',
      ma_hd: state.hopDong?.uuid || '',
      ma_dtt: state.dotThanhToan?.uuid || '',
      ma_ku: state.kheUoc?.uuid || '',
      ma_phi: state.phi?.uuid || '',
      ma_sp: state.sanPham?.uuid || '',
      ma_lsx: state.lenhSanXuat?.uuid || ''
    });
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={'Bảng xác nhận công nợ'}
      maxWidth='xl'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode={formMode}
        hasAritoActionBar={false}
        schema={SearchFormSchema}
        onSubmit={handleSubmit}
        initialData={initialData}
        className='w-[900px]'
        headerFields={<BasicInfoTab formMode={formMode} />}
        tabs={[
          {
            id: 'detail',
            label: 'Chi tiết',
            component: <DetailTab formMode={formMode} searchState={{ state, actions }} />
          },
          {
            id: 'other',
            label: 'Khác',
            component: <OtherTab formMode={formMode} />
          },
          {
            id: 'filter',
            label: 'Đối tượng lọc',
            component: <FilterTab formMode={formMode} searchState={{ state, actions }} />
          }
        ]}
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={<BottomBar mode={formMode} onClose={onClose} />}
      />
    </AritoDialog>
  );
};

export default SearchDialog;
