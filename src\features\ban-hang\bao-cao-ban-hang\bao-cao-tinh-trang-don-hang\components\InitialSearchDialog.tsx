import React, { useState } from 'react';

import { Button } from '@mui/material';
import { AritoDialog, AritoForm, AritoHeaderTabs } from '@/components/custom/arito';
import { DetailsTab, BasicInfo, OtherTab } from './tabs';
import { searchSchema, initialValues } from '../schema';
import AritoIcon from '@/components/custom/arito/icon';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const [formValues, setFormValues] = useState(initialValues);

  const handleSubmit = (data: any) => {
    onSearch(data);
    onClose();
    console.log('Form submitted with data:', data);
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Báo cáo tình trạng đơn hàng'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={data => {
          setFormValues(data);
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailsTab />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 px-2'
        bottomBar={
          <>
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              type='submit'
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>

            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
