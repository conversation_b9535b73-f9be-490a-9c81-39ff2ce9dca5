import { useFormContext } from 'react-hook-form';
import {
  transactionCodeSearchColumns,
  departmentSearchColumns,
  incidentSearchColumns,
  contractSearchColumns,
  employeeSearchColumns,
  batchSearchColumns,
  accountSearchColumns,
  locationSearchColumns
} from './cols-definition';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import DocumentNumberRange from '../DocumentNumberRange';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

const OtherTab: React.FC = () => {
  const { setValue } = useFormContext();

  return (
    <div className='w-[800px] min-w-[800px] space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* Transaction Code */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã giao dịch:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/`}
              searchColumns={transactionCodeSearchColumns}
              dialogTitle='Danh mục mã giao dịch'
              columnDisplay='ma_gd'
              displayRelatedField='ten_gd'
              onValueChange={value => {
                setValue('ma_giao_dich', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_giao_dich', row.ma_gd);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Material Account */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Tài khoản vật tư:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              dialogTitle='Danh mục tài khoản'
              columnDisplay='code'
              displayRelatedField='name'
              onValueChange={value => {
                setValue('tai_khoan_vat_tu', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('tai_khoan_vat_tu', row.code);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Department Code */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã bộ phận:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
              searchColumns={departmentSearchColumns}
              dialogTitle='Danh mục bộ phận'
              columnDisplay='ma_bp'
              displayRelatedField='ten_bp'
              onValueChange={value => {
                setValue('ma_bo_phan', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_bo_phan', row.ma_bp);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Incident Code */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã vụ việc:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
              searchColumns={incidentSearchColumns}
              dialogTitle='Danh mục vụ việc'
              columnDisplay='ma_vv'
              displayRelatedField='ten_vv'
              onValueChange={value => {
                setValue('ma_vu_viec', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_vu_viec', row.ma_vv);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Contract Code */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã hợp đồng:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
              searchColumns={contractSearchColumns}
              dialogTitle='Danh mục hợp đồng'
              columnDisplay='ma_hd'
              displayRelatedField='ten_hd'
              onValueChange={value => {
                setValue('ma_hop_dong', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_hop_dong', row.ma_hd);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Employee Code */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã nhân viên:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHAN_VIEN}/`}
              searchColumns={employeeSearchColumns}
              dialogTitle='Danh mục nhân viên'
              columnDisplay='ma_nhan_vien'
              displayRelatedField='ho_ten_nhan_vien'
              onValueChange={value => {
                setValue('ma_nhan_vien', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_nhan_vien', row.ma_nhan_vien);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Business Account */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Tài khoản doanh thu:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              dialogTitle='Danh mục tài khoản'
              columnDisplay='code'
              displayRelatedField='name'
              onValueChange={value => {
                setValue('tai_khoan_doanh_thu', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('tai_khoan_doanh_thu', row.code);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Capital Account */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Tài khoản giá vốn:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              dialogTitle='Danh mục tài khoản'
              columnDisplay='code'
              displayRelatedField='name'
              onValueChange={value => {
                setValue('tai_khoan_gia_von', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('tai_khoan_gia_von', row.code);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Batch Code */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã lô:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.LO}/`}
              searchColumns={batchSearchColumns}
              dialogTitle='Danh mục lô hàng'
              columnDisplay='ma_lo'
              displayRelatedField='ten_lo'
              onValueChange={value => {
                setValue('ma_lo', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_lo', row.ma_lo);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã vị trí:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VI_TRI}/`}
              searchColumns={locationSearchColumns}
              dialogTitle='Danh mục vị trí'
              columnDisplay='ma_vt'
              displayRelatedField='ten_vt'
              onValueChange={value => {
                setValue('ma_vi_tri', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_vi_tri', row.ma_vt);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Số c/từ (từ/đến):</Label>
          <DocumentNumberRange fromDocumentNumberName='fromDocumentNumber' toDocumentNumberName='toDocumentNumber' />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Diễn giải:</Label>
          <div className='w-[57.5%]'>
            <FormField name='dien_giai' type='text' placeholder='Nhập diễn giải' className='w-full' />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mẫu lọc báo cáo:</Label>
          <div className='w-full'>
            <div className='flex items-center gap-1'>
              <div className='w-64'>
                <FormField
                  name='mau_loc_bao_cao'
                  label=''
                  type='select'
                  options={[{ value: 'user_filter', label: 'Người dùng tự lọc' }]}
                  className='w-full'
                />
              </div>

              <div className='h-9 w-9 flex-shrink-0'>
                <RadixHoverDropdown
                  iconNumber={624}
                  items={[
                    {
                      value: 'save_new',
                      label: 'Lưu mẫu mới',
                      icon: 7,
                      onClick: () => console.log('Save new filter template')
                    },
                    {
                      value: 'save_overwrite',
                      label: 'Lưu đè vào mẫu đang chọn',
                      icon: 75,
                      onClick: () => console.log('Overwrite current filter template')
                    },
                    {
                      value: 'delete',
                      label: 'Xóa mẫu đang chọn',
                      icon: 8,
                      onClick: () => console.log('Delete current filter template')
                    }
                  ]}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OtherTab;
