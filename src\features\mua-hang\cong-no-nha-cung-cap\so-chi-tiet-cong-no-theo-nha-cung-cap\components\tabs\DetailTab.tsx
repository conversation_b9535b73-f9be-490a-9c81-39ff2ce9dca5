import { useFormContext } from 'react-hook-form';
import React from 'react';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS, supplierSearchColumns } from '@/constants';
import { Label } from '@/components/ui/label';

const DetailTab: React.FC = () => {
  const { setValue } = useFormContext();

  return (
    <div className='w-[800px] min-w-[800px] space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* Mã nhà cung cấp */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã nhà cung cấp:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHA_CUNG_CAP}/`}
              searchColumns={supplierSearchColumns}
              dialogTitle='Danh mục nhà cung cấp'
              columnDisplay='customer_code'
              displayRelatedField='customer_name'
              onValueChange={value => {
                setValue('ma_ncc', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_ncc', row.customer_code);
                  setValue('ten_ncc', row.customer_name);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mẫu báo cáo */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mẫu báo cáo:</Label>
          <div className='w-64'>
            <FormField
              name='mau_bao_cao'
              label=''
              type='select'
              options={[
                { value: 'tien_chuan', label: 'Mẫu tiền chuẩn' },
                { value: 'ngoai_te', label: 'Mẫu ngoại tệ' }
              ]}
              className='w-full'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailTab;
