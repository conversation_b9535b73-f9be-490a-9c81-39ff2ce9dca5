'use client';

import { useState } from 'react';
import { useDialogState, useTableData, useBangTinhKhauHaoTSCDTT } from './hooks';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { InitialSearchDialog, ActionBar } from './components';

export default function BangTinhKhauHaoTSCDTheoThang() {
  const { initialSearchDialogOpen, showTable, handleInitialSearchClose, handleInitialSearch, handleSearchClick } =
    useDialogState();

  const [subTitle, setSubTitle] = useState<string>('');
  const [searchParams, setSearchParams] = useState<any>({});
  const { data, isLoading, fetchData, refreshData } = useBangTinhKhauHaoTSCDTT(searchParams);
  const { tables } = useTableData(data);

  const handleSearchWithData = async (values: any) => {
    setSearchParams(values);
    setSubTitle(
      `Từ kỳ ${values.tu_ky || ''} năm ${values.tu_nam || ''} đến kỳ ${values.den_ky || ''} năm ${values.den_nam || ''}`
    );
    handleInitialSearch(values, fetchData);
  };

  const handleRefreshWithData = async () => {
    await refreshData();
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleSearchWithData}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshWithData}
            onFixedColumnsClick={() => {}}
            className='border-b border-gray-200'
            subTitle={subTitle}
          />

          <div className='flex-1 overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && <AritoDataTables tables={tables} />}
          </div>
        </>
      )}
    </div>
  );
}
