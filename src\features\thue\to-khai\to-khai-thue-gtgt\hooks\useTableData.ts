import { useState } from 'react';
import {
  bangKeBanRaColumns,
  bangKeMuaVaoColumns,
  pl142Columns,
  vatTaxDeclarationColumns,
  vatTaxDeclarationPL43Columns
} from '../cols-definition';
import {
  VatTaxDeclarationItem,
  VatTaxDeclarationPL43Item,
  BangKeBanRaItem,
  PL142Item,
  BangKeMuaVaoItem
} from '@/types/schemas';

export interface TableConfig {
  id: string;
  name: string;
  title: string;
  rows: any[];
  columns: any[];
  actionBarConfig: {
    showLapToKhai: boolean;
    showTinhLai: boolean;
    showCoDinhCot: boolean;
    showChinhSuaMauIn: boolean;
    showKetXuatDuLieu: boolean;
  };
}

// Mock data generation functions
const generateVatTaxDeclarationData = (): VatTaxDeclarationItem[] => {
  const vatItems = [
    { chi_tieu: '<PERSON><PERSON><PERSON> số hàng hóa, dịch vụ bán ra chịu thuế GTGT', ma_so: '01' },
    { chi_tieu: '<PERSON><PERSON><PERSON> số hàng hóa, dịch vụ bán ra không chịu thuế GTGT', ma_so: '02' },
    { chi_tieu: 'Doanh số hàng hóa, dịch vụ bán ra được miễn thuế GTGT', ma_so: '03' },
    { chi_tieu: 'Doanh số hàng hóa, dịch vụ xuất khẩu', ma_so: '04' },
    { chi_tieu: 'Thuế GTGT của hàng hóa, dịch vụ bán ra', ma_so: '20' },
    { chi_tieu: 'Thuế GTGT của hàng hóa, dịch vụ mua vào được khấu trừ', ma_so: '21' },
    { chi_tieu: 'Thuế GTGT còn được khấu trừ kỳ trước chuyển sang', ma_so: '22' },
    { chi_tieu: 'Tổng số thuế GTGT được khấu trừ', ma_so: '23' },
    { chi_tieu: 'Số thuế GTGT phải nộp', ma_so: '24' },
    { chi_tieu: 'Số thuế GTGT còn được khấu trừ chuyển kỳ sau', ma_so: '25' }
  ];

  return vatItems.map((item, index) => ({
    id: (index + 1).toString(),
    stt_in: (index + 1).toString(),
    chi_tieu: item.chi_tieu,
    ma_so: item.ma_so,
    doanh_so: Math.floor(Math.random() * *********0) + *********,
    thue: Math.floor(Math.random() * *********) + 10000000
  }));
};

const generateVatTaxDeclarationPL43Data = (): VatTaxDeclarationPL43Item[] => {
  const products = [
    'Máy tính xách tay',
    'Điện thoại thông minh',
    'Máy in laser',
    'Màn hình máy tính',
    'Bàn phím cơ',
    'Chuột không dây',
    'Tai nghe Bluetooth',
    'Ổ cứng SSD',
    'RAM DDR4',
    'Card đồ họa'
  ];

  return products.map((product, index) => ({
    id: (index + 1).toString(),
    stt_in: (index + 1).toString(),
    ten_hhdv: product,
    ma_so: `SP${String(index + 1).padStart(3, '0')}`,
    tien_chua_thue: Math.floor(Math.random() * 50000000) + 10000000,
    thue_suat: [0, 5, 10][Math.floor(Math.random() * 3)],
    thue_suat_duoc_giam: [0, 2, 5][Math.floor(Math.random() * 3)],
    thue_duoc_giam: Math.floor(Math.random() * 5000000) + 1000000
  }));
};

const generateBangKeBanRaData = (): BangKeBanRaItem[] => {
  const customers = [
    'Công ty TNHH ABC Technology',
    'Công ty CP XYZ Solutions',
    'Doanh nghiệp tư nhân DEF',
    'Công ty TNHH GHI Trading',
    'Tập đoàn JKL Group'
  ];

  const products = [
    'Phần mềm quản lý',
    'Dịch vụ tư vấn IT',
    'Thiết bị máy tính',
    'Dịch vụ bảo trì',
    'Giải pháp công nghệ'
  ];

  return Array.from({ length: 15 }, (_, index) => {
    const currentDate = new Date();
    const randomDays = Math.floor(Math.random() * 30);
    const invoiceDate = new Date(currentDate.getTime() - randomDays * 24 * 60 * 60 * 1000);

    return {
      id: (index + 1).toString(),
      so_hoa_don: `HD${String(index + 1).padStart(6, '0')}`,
      ngay_hoa_don: invoiceDate, // Keep as Date object for MUI DataGrid
      ky_hieu: `AA/24E`,
      ten_khach_hang: customers[index % customers.length],
      ten_mat_hang: products[index % products.length],
      ma_so_thue: `${*********0 + index * 111111}`,
      tien_ban: Math.floor(Math.random() * *********) + 10000000,
      thue_suat: [0, 5, 10][Math.floor(Math.random() * 3)],
      tien_thue: Math.floor(Math.random() * 10000000) + 1000000,
      ghi_chu: `Ghi chú hóa đơn ${index + 1}`
    };
  });
};

const generatePL142Data = (): PL142Item[] => {
  const services = [
    'Dịch vụ tư vấn công nghệ thông tin',
    'Dịch vụ phát triển phần mềm',
    'Dịch vụ bảo trì hệ thống',
    'Dịch vụ đào tạo nhân viên',
    'Dịch vụ thiết kế website',
    'Dịch vụ quản lý dữ liệu',
    'Dịch vụ an ninh mạng',
    'Dịch vụ cloud computing'
  ];

  return services.map((service, index) => ({
    id: (index + 1).toString(),
    ten_hang_hoa_dich_vu: service,
    ma_so: `DV${String(index + 1).padStart(3, '0')}`,
    tien_chua_thue_nt: Math.floor(Math.random() * 50000000) + 10000000,
    tien_chua_thue: Math.floor(Math.random() * 50000000) + 10000000,
    thue_suat_quy_dinh: [0, 5, 10][Math.floor(Math.random() * 3)],
    thue_suat_sau_giam: [0, 3, 8][Math.floor(Math.random() * 3)],
    thue_duoc_giam_nt: Math.floor(Math.random() * 2000000) + 500000,
    thue_duoc_giam: Math.floor(Math.random() * 2000000) + 500000,
    thue_gtgt_cua_hh_dv_nt: Math.floor(Math.random() * 5000000) + 1000000,
    thue_gtgt_cua_hh_dv: Math.floor(Math.random() * 5000000) + 1000000
  }));
};

const generateBangKeMuaVaoData = (): BangKeMuaVaoItem[] => {
  const suppliers = [
    'Công ty TNHH Phần mềm Việt',
    'Công ty CP Công nghệ ABC',
    'Doanh nghiệp XYZ Solutions',
    'Công ty TNHH DEF Trading',
    'Tập đoàn GHI Technology'
  ];

  const materials = ['Thiết bị máy tính', 'Phần mềm bản quyền', 'Dịch vụ internet', 'Thiết bị mạng', 'Dịch vụ hosting'];

  return Array.from({ length: 12 }, (_, index) => {
    const currentDate = new Date();
    const randomDays = Math.floor(Math.random() * 30);
    const invoiceDate = new Date(currentDate.getTime() - randomDays * 24 * 60 * 60 * 1000);
    const tienChuaThue = Math.floor(Math.random() * 50000000) + 10000000;
    const thueSuat = [0, 5, 10][Math.floor(Math.random() * 3)];
    const tienThue = (tienChuaThue * thueSuat) / 100;

    return {
      id: (index + 1).toString(),
      so_hoa_don: `MV${String(index + 1).padStart(6, '0')}`,
      ngay_hoa_don: invoiceDate, // Keep as Date object for MUI DataGrid
      so_chung_tu: `CT${String(index + 1).padStart(6, '0')}`,
      ky_hieu: `BB/24E`,
      ten_khach_hang: suppliers[index % suppliers.length],
      ma_so_thue: `${2000000000 + index * 222222}`,
      ten_mat_hang: materials[index % materials.length],
      tien_chua_thue: tienChuaThue,
      thue_suat: thueSuat,
      tien_thue: tienThue,
      tong_tien: tienChuaThue + tienThue,
      ghi_chu: `Ghi chú mua vào ${index + 1}`
    };
  });
};

export function useTableData() {
  const [currentTableId, setCurrentTableId] = useState('to-khai-gtgt');

  const [tables] = useState<TableConfig[]>([
    {
      id: 'to-khai-gtgt',
      name: 'Tờ khai thuế GTGT (01/GTGT)(TT80/2021)',
      title: 'Tờ khai thuế giá trị gia tăng',
      rows: generateVatTaxDeclarationData(),
      columns: vatTaxDeclarationColumns,
      actionBarConfig: {
        showLapToKhai: true,
        showTinhLai: true,
        showCoDinhCot: false,
        showChinhSuaMauIn: false,
        showKetXuatDuLieu: false
      }
    },
    {
      id: 'to-khai-gtgt-pl43',
      name: 'Tờ khai thuế GTGT PL43',
      title: 'Tờ khai thuế GTGT PL43/2022/QH15',
      rows: generateVatTaxDeclarationPL43Data(),
      columns: vatTaxDeclarationPL43Columns,
      actionBarConfig: {
        showLapToKhai: false,
        showTinhLai: false,
        showCoDinhCot: true,
        showChinhSuaMauIn: true,
        showKetXuatDuLieu: false
      }
    },
    {
      id: 'pl-giam-thue-gtgt',
      name: 'PL_GiamThue_GTGT_23_24',
      title: 'PL Giảm thuế GTGT 23-24',
      rows: generateVatTaxDeclarationPL43Data(),
      columns: vatTaxDeclarationPL43Columns,
      actionBarConfig: {
        showLapToKhai: false,
        showTinhLai: false,
        showCoDinhCot: true,
        showChinhSuaMauIn: true,
        showKetXuatDuLieu: false
      }
    },
    {
      id: 'bang-ke-ban-ra',
      name: 'Bảng kê bán ra 01-1/GTGT',
      title: 'Bảng kê bán ra 01-1/GTGT',
      rows: generateBangKeBanRaData(),
      columns: bangKeBanRaColumns,
      actionBarConfig: {
        showLapToKhai: false,
        showTinhLai: false,
        showCoDinhCot: true,
        showChinhSuaMauIn: false,
        showKetXuatDuLieu: true
      }
    },
    {
      id: 'to-khai-gtgt-pl142',
      name: 'Tờ khai thuế GTGT PL142',
      title: 'Tờ khai thuế GTGT PL142',
      rows: generatePL142Data(),
      columns: pl142Columns,
      actionBarConfig: {
        showLapToKhai: false,
        showTinhLai: false,
        showCoDinhCot: true,
        showChinhSuaMauIn: true,
        showKetXuatDuLieu: false
      }
    },
    {
      id: 'bang-ke-mua-vao',
      name: 'Bảng kê mua vào 01-2/GTGT',
      title: 'Bảng kê mua vào 01-2/GTGT',
      rows: generateBangKeMuaVaoData(),
      columns: bangKeMuaVaoColumns,
      actionBarConfig: {
        showLapToKhai: false,
        showTinhLai: false,
        showCoDinhCot: true,
        showChinhSuaMauIn: false,
        showKetXuatDuLieu: true
      }
    }
  ]);

  const currentTable = tables.find(table => table.id === currentTableId) || tables[0];

  const handleRowClick = (row: any) => {
    console.log('Row clicked:', row);
  };

  const updateTableColumns = (templateValue: string) => {
    console.log('Update table columns:', templateValue);
  };

  const switchTable = (tableId: string) => {
    setCurrentTableId(tableId);
  };

  return {
    tables,
    currentTable,
    currentTableId,
    handleRowClick,
    updateTableColumns,
    switchTable
  };
}
