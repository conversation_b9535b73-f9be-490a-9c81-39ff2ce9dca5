import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { BangTinhKhauHaoTSCDTTItem } from '@/types/schemas';

export interface UseTableDataReturn {
  tables: TableData[];
}

export function useTableData(data: BangTinhKhauHaoTSCDTTItem[]): UseTableDataReturn {
  const tables = useMemo(() => {
    const tableData: TableData[] = [
      {
        name: '',
        columns: [
          {
            field: 'ma_ts',
            headerName: 'Mã tài sản',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ma_ts || '';
            }
          },
          {
            field: 'ten_ts',
            headerName: 'Tên tài sản',
            width: 250
          },
          {
            field: 'ten_dvt',
            headerName: 'Đơn vị tính',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ten_dvt || '';
            }
          },
          {
            field: 'so_luong',
            headerName: 'Số lượng',
            width: 100,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.so_luong || 0;
              return value.toLocaleString('vi-VN');
            }
          },
          {
            field: 'ma_lts',
            headerName: 'Mã loại TS',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ma_lts || '';
            }
          },
          {
            field: 'ten_lts',
            headerName: 'Tên loại tài sản',
            width: 150,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ten_lts || '';
            }
          },
          {
            field: 'ngay_kh0',
            headerName: 'Ngày KH ban đầu',
            width: 140,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ngay_kh0 || '';
            }
          },
          {
            field: 'so_ky_kh',
            headerName: 'Số kỳ KH',
            width: 100,
            type: 'number',
            renderCell: (params: any) => {
              if (params.row.isTotal) return '';
              return params.row.so_ky_kh || 0;
            }
          },
          {
            field: 'ngay_kh_kt',
            headerName: 'Ngày KH kết thúc',
            width: 140,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ngay_kh_kt || '';
            }
          },
          {
            field: 'tk_ts',
            headerName: 'TK tài sản',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.tk_ts || '';
            }
          },
          {
            field: 'ma_bp',
            headerName: 'Mã bộ phận',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ma_bp || '';
            }
          },
          {
            field: 'systotal',
            headerName: 'Tiền',
            width: 140,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.systotal || 0;
              return value.toLocaleString('vi-VN');
            }
          }
        ],
        rows: data
      }
    ];

    return tableData;
  }, [data]);

  return {
    tables
  };
}
