import { useState } from 'react';
import { boPhanSearchColumns, groupColumns, loaiTaiSanSearchColumns } from '@/constants/search-columns';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import type { SaveTemplateFormData } from '../SaveTemplateDialog';
import type { BoPhan, LoaiTSCDCCDC } from '@/types/schemas';
import SaveTemplateDialog from '../SaveTemplateDialog';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

interface DetailsTabProps {
  searchFieldStates: {
    loaiTaiSan: LoaiTSCDCCDC | null;
    setLoaiTaiSan: (loaiTaiSan: LoaiTSCDCCDC | null) => void;
    boPhan: <PERSON><PERSON><PERSON> | null;
    setBoPhan: (boPhan: <PERSON><PERSON><PERSON> | null) => void;
    nhom1: any | null;
    setNhom1: (nhom1: any | null) => void;
    nhom2: any | null;
    setNhom2: (nhom2: any | null) => void;
    nhom3: any | null;
    setNhom3: (nhom3: any | null) => void;
  };
}

const DetailsTab: React.FC<DetailsTabProps> = ({ searchFieldStates }) => {
  const { loaiTaiSan, setLoaiTaiSan, boPhan, setBoPhan, nhom1, setNhom1, nhom2, setNhom2, nhom3, setNhom3 } =
    searchFieldStates;
  const [saveAnalysisTemplateDialogOpen, setSaveAnalysisTemplateDialogOpen] = useState(false);

  const handleSaveAnalysisTemplate = (data: SaveTemplateFormData) => {
    console.log('Saving analysis template:', data);
    setSaveAnalysisTemplateDialogOpen(false);
  };
  return (
    <div className='min-w-[850px] space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Loại công cụ</Label>
          <SearchField<LoaiTSCDCCDC>
            searchEndpoint={`/${QUERY_KEYS.LOAI_TAI_SAN_CONG_CU}/`}
            searchColumns={loaiTaiSanSearchColumns}
            dialogTitle='Danh mục loại công cụ'
            columnDisplay='ma_lts'
            displayRelatedField='ten_lts'
            value={loaiTaiSan?.ma_lts || ''}
            onRowSelection={setLoaiTaiSan}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Bộ phận sử dụng</Label>
          <SearchField<BoPhan>
            searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
            searchColumns={boPhanSearchColumns}
            dialogTitle='Danh mục bộ phận'
            columnDisplay='ma_bp'
            displayRelatedField='ten_bp'
            value={boPhan?.ma_bp || ''}
            onRowSelection={setBoPhan}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Nhóm công cụ 1,2,3</Label>
          <div className='flex gap-2'>
            <SearchField<any>
              searchEndpoint={`/${QUERY_KEYS.NHOM_TSCD}/`}
              searchColumns={groupColumns}
              dialogTitle='Danh mục nhóm công cụ 1'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              value={nhom1?.ma_nhom || ''}
              onRowSelection={setNhom1}
            />
            <SearchField<any>
              searchEndpoint={`/${QUERY_KEYS.NHOM_TSCD}/`}
              searchColumns={groupColumns}
              dialogTitle='Danh mục nhóm công cụ 2'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              value={nhom2?.ma_nhom || ''}
              onRowSelection={setNhom2}
            />
            <SearchField<any>
              searchEndpoint={`/${QUERY_KEYS.NHOM_TSCD}/`}
              searchColumns={groupColumns}
              dialogTitle='Danh mục nhóm công cụ 3'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              value={nhom3?.ma_nhom || ''}
              onRowSelection={setNhom3}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu phân tích DL:</Label>
          <div className='flex flex-1 items-center gap-1'>
            <div className=''>
              <FormField
                name='data_analysis_struct'
                type='select'
                className='w-96'
                options={[
                  { value: '1', label: 'Bảng tính phân bổ CCDC theo tháng (Mẫu chuẩn)' },
                  { value: '2', label: 'Bảng tính phân bổ CCDC theo tháng (Mẫu ngoại tệ)' }
                ]}
              />
            </div>
            <div className='flex-1'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={873}
                items={[
                  {
                    value: 'save_new',
                    label: 'Tạo mới mẫu phân tích',
                    icon: 7,
                    onClick: () => setSaveAnalysisTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
        {/* Save Analysis Template Dialog */}
        <SaveTemplateDialog
          open={saveAnalysisTemplateDialogOpen}
          onClose={() => setSaveAnalysisTemplateDialogOpen(false)}
          onSave={handleSaveAnalysisTemplate}
          templateType='analysis'
        />
      </div>
    </div>
  );
};

export default DetailsTab;
