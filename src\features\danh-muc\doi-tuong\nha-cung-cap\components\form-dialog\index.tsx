import React from 'react';
import { BasicInformationTab, GeneralTab, AccountTab, OtherTab, HDDTTab } from './tabs';
import { nhaCungCapSchema, initialNhaCungCapValues } from '../../schema';
import { AritoForm } from '@/components/custom/arito/form';

interface FormDialogProps {
  formMode: 'add' | 'edit' | 'view';
  initialData?: any;
  selectedObj?: any; // Data from selectedObj when copying
  onSubmit: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

export const FormDialog: React.FC<FormDialogProps> = ({
  formMode,
  initialData,
  selectedObj,
  onSubmit,
  onClose,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}) => {
  const handleSubmit = async (data: any) => {
    onSubmit(data);
  };

  return (
    <div className='h-full flex-1 lg:overflow-hidden'>
      <AritoForm
        mode={formMode}
        initialData={initialData || initialNhaCungCapValues}
        onSubmit={handleSubmit}
        headerFields={<BasicInformationTab formMode={formMode} />}
        tabs={[
          {
            id: 'thong-tin-chung',
            label: 'Thông tin chung',
            component: <GeneralTab formMode={formMode} selectedObj={selectedObj} />
          },
          {
            id: 'khac',
            label: 'Khác',
            component: <OtherTab formMode={formMode} />
          },
          {
            id: 'thong-tin-them',
            label: 'Thông tin thêm',
            component: <AccountTab formMode={formMode} />
          },
          {
            id: 'hddt',
            label: 'HĐĐT(1. Co)',
            component: <HDDTTab formMode={formMode} />
          }
        ]}
        onClose={onClose}
        subTitle={'Nhà cung cấp'}
      />
    </div>
  );
};

export default FormDialog;
