'use client';

import { useState } from 'react';
import { EditPrintTemplateDialog } from '@/components/custom/arito/edit-print-template';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { useDialogState, useSoDoiChieuCongNo, useTableData } from './hooks';
import { ActionBar, SearchDialog } from './components';
import { initialSearchValues } from './schema';
import { useRows } from '@/hooks';

export default function SoDoiChieuCongNoPage() {
  const [searchParams, setSearchParams] = useState<any>({});
  const { selectedObj, selectedRowIndex, clearSelection } = useRows();
  const {
    showData,
    showSearchDialog,
    showEditPrintTemplateDialog,

    openSearchDialog,
    closeSearchDialog,
    openEditPrintTemplateDialog,
    closeEditPrintTemplateDialog,

    handleSearchButtonClick,
    handleEditPrintTemplateButtonClick
  } = useDialogState(clearSelection);

  const { data, isLoading, fetchData, refreshData } = useSoDoiChieuCongNo(searchParams);
  const { tables, handleRowClick } = useTableData(data, searchParams);

  const handleSubmit = async (data: any) => {
    setSearchParams(data);
    await fetchData(data);
    handleSearchButtonClick();
  };

  const handleRefreshClick = async () => {
    await refreshData();
  };

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog
          open={showSearchDialog}
          onClose={closeSearchDialog}
          onSubmit={handleSubmit}
          formMode={'add'}
          initialData={initialSearchValues}
        />
      )}

      <EditPrintTemplateDialog
        open={showEditPrintTemplateDialog}
        onClose={closeEditPrintTemplateDialog}
        onSave={handleEditPrintTemplateButtonClick}
      />

      {isLoading && <LoadingOverlay />}
      {showData && (
        <>
          <ActionBar
            onSearchClick={openSearchDialog}
            onPrintClick={() => {}}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={() => {}}
            onExportClick={() => {}}
            onEditPrintClick={openEditPrintTemplateDialog}
            searchParams={searchParams}
          />

          <div className='w-full overflow-hidden'>
            {!isLoading && (
              <AritoDataTables
                tables={tables}
                selectedRowId={selectedRowIndex || undefined}
                onRowClick={handleRowClick}
              />
            )}
          </div>
        </>
      )}
    </div>
  );
}
