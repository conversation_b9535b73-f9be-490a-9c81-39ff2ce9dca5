import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { taiSanSearchColumns } from '@/constants/search-columns';
import type { TaiSanCoDinh } from '@/types/schemas';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

interface BasicInfoProps {
  searchFieldStates: {
    taiSan: TaiSanCoDinh | null;
    setTaiSan: (taiSan: TaiSanCoDinh | null) => void;
  };
}

const BasicInfo: React.FC<BasicInfoProps> = ({ searchFieldStates }) => {
  const { taiSan, setTaiSan } = searchFieldStates;
  return (
    <div className='min-w-[850px] space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Từ kỳ/năm</Label>
          <div className='flex items-center gap-2'>
            <FormField name='tu_ky' type='number' className='w-20' />
            <FormField name='tu_nam' type='number' className='w-24' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Đến kỳ/năm</Label>
          <div className='flex items-center gap-2'>
            <FormField name='den_ky' type='number' className='w-20' />
            <FormField name='den_nam' type='number' className='w-24' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Tài sản</Label>
          <SearchField<TaiSanCoDinh>
            searchEndpoint={`/${QUERY_KEYS.KHAI_BAO_THONG_TIN_TSCD}/`}
            searchColumns={taiSanSearchColumns}
            dialogTitle='Danh mục tài sản'
            columnDisplay='ma_ts'
            displayRelatedField='ten_ts'
            value={taiSan?.ma_ts || ''}
            onRowSelection={setTaiSan}
          />
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
