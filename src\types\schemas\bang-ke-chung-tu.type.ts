/**
 * TypeScript interfaces for BangKeChungTu (Document Listing) model
 *
 * This interface represents the structure of the BangKeChungTu model from the backend.
 * It defines document listing data structures for financial reporting.
 */

import { ApiResponse } from '../api.type';

// Main BangKeChungTu Item
export interface BangKeChungTuItem {
  id: string; // ID
  unit_id: string; // Unit ID
  ma_unit: string; // Unit code
  ngay_ct: Date | string; // Document date
  so_ct: string; // Document number
  ma_kh: string; // Customer code
  ten_kh: string; // Customer name
  dien_giai: string; // Description
  tk: string; // Account
  tk_du: string; // Counter account
  ps_no: number; // Debit amount
  ps_co: number; // Credit amount
  ma_bp: string; // Department code
  ma_vv: string; // Case code
  ma_hd: string; // Contract code
  ma_dtt: string; // Payment phase code
  ma_ku: string; // Agreement code
  ma_phi: string; // Fee code
  ma_sp: string; // Product code
  ma_lsx: string; // Production order code
  ma_cp0: string; // CP0 code
  ma_ct: string; // Document type code
  isTotal?: boolean; // Flag to identify total rows
  stt?: number; // Serial number (optional)
  so_ct0?: string; // Document number 0 (optional)
  ngay_ct0?: Date | string; // Document date 0 (optional)
  nh_dk?: string; // Registered bank (optional)
  xgroup?: string; // Group (optional)
}

// API Response types
export interface BangKeChungTuResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BangKeChungTuItem[];
}

// Search form values interface
export interface BangKeChungTuSearchFormValues {
  ngay_ct1?: Date; // From date
  ngay_ct2?: Date; // To date
  so_ct1?: string; // Document number from
  so_ct2?: string; // Document number to
  no_co?: string; // Debit/Credit selection
  mau_bc?: string; // Report template
  report_filtering?: string; // Report filtering template
  data_analysis_struct?: string; // Data analysis structure template
  [key: string]: any;
}

// Hook return types
export interface UseBangKeChungTuReturn {
  data: BangKeChungTuItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: BangKeChungTuSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
