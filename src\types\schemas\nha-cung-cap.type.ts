/**
 * TypeScript interface for NhaCungCap (Vendor/Supplier) model
 *
 * This interface represents the structure of the NhaCungCap model from the backend.
 * It defines vendors/suppliers used in the system.
 */

import { NhanVien } from './nhan-vien.type';
import { NhomHang } from './nhom-hang.type';
import { ApiResponse } from '../api.type';
import { Group } from './group.type';

export interface NhaCungCap {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Customer/Vendor code
   */
  customer_code: string;

  /**
   * Customer type (1=individual, 2=enterprise)
   */
  customer_type?: string | null;

  /**
   * Is customer flag
   */
  is_customer?: boolean;

  /**
   * Is vendor flag
   */
  is_vendor?: boolean;

  /**
   * Customer/Vendor name
   */
  customer_name: string;

  /**
   * Alternative name
   */
  alternative_name?: string | null;

  /**
   * Address
   */
  address?: string | null;

  /**
   * Tax identification number
   */
  tax_code?: string | null;

  /**
   * Contact person
   */
  contact_person?: string | null;

  /**
   * Enterprise name
   */
  enterprise_name?: string | null;

  /**
   * Phone number
   */
  phone?: string | null;

  /**
   * Fax number
   */
  fax?: string | null;

  /**
   * Email address
   */
  email?: string | null;

  /**
   * Website
   */
  website?: string | null;

  /**
   * Legal representative
   */
  legal_representative?: string | null;

  /**
   * Representative position
   */
  representative_position?: string | null;

  /**
   * Representative
   */
  representative?: string | null;

  /**
   * Account reference
   */
  account?: string | null;

  /**
   * Payment term reference
   */
  payment_term?: string | null;

  /**
   * Payment method reference
   */
  payment_method?: string | null;

  /**
   * Credit limit
   */
  credit_limit?: string | null;

  /**
   * Bank account number
   */
  bank_account?: string | null;

  /**
   * Bank name
   */
  bank_name?: string | null;

  /**
   * Bank branch
   */
  bank_branch?: string | null;

  /**
   * Customer group 1
   */
  customer_group1?: string | null;

  /**
   * Customer group 2
   */
  customer_group2?: string | null;

  /**
   * Customer group 3
   */
  customer_group3?: string | null;

  /**
   * Customer group data 1
   */
  customer_group1_data?: Group | null;

  /**
   * Customer group data 2
   */
  customer_group2_data?: Group | null;

  /**
   * Customer group data 3
   */
  customer_group3_data?: Group | null;

  /**
   * Region reference
   */
  region?: string | null;

  /**
   * Sales representative
   */
  sales_rep?: string | null;

  /**
   * Sales representative data
   */
  sales_rep_data?: NhanVien | null;

  /**
   * Search keywords
   */
  search_keywords?: string | null;

  /**
   * Province
   */
  province?: string | null;

  /**
   * Notes/comments
   */
  notes?: string | null;

  /**
   * Status
   */
  status?: string | null;

  /**
   * Birth date
   */
  birth_date?: string | null;

  /**
   * ID number
   */
  id_number?: string | null;

  /**
   * Description
   */
  description?: string | null;

  /**
   * Delivery address
   */
  delivery_address?: string | null;

  /**
   * Business field
   */
  business_field?: string | null;

  /**
   * Use e-invoice
   */
  use_einvoice?: string | null;

  /**
   * E-invoice email
   */
  einvoice_email?: string | null;

  /**
   * Customer number
   */
  customer_number?: string | null;

  /**
   * Reference to the entity model
   */
  entity_model?: string;

  /**
   * Active flag
   */
  active?: boolean;

  /**
   * Hidden flag
   */
  hidden?: boolean;

  /**
   * Additional info
   */
  additional_info?: any;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Type for NhaCungCap API response
 */
export type NhaCungCapResponse = ApiResponse<NhaCungCap>;

/**
 * Type for creating or updating a NhaCungCap
 */
export interface NhaCungCapInput {
  /**
   * Vendor number
   */
  customer_code?: string | null;

  /**
   * Vendor name
   */
  customer_name: string;

  /**
   * Description
   */
  description: string;

  /**
   * Active flag
   */
  active?: boolean;

  /**
   * Hidden flag
   */
  hidden?: boolean;

  /**
   * Supplier code
   */
  ma_kh?: string | null;

  /**
   * Is customer flag
   */
  kh_yn?: boolean;

  /**
   * Is supplier flag
   */
  ncc_yn?: boolean;

  /**
   * Customer type
   */
  loai_kh?: string | null;

  /**
   * Supplier name
   */
  ten_kh?: string | null;

  /**
   * Alternative name
   */
  ten_kh2?: string | null;

  /**
   * Address
   */
  dia_chi?: string | null;

  /**
   * Tax code
   */
  ma_so_thue?: string | null;

  /**
   * Contact person
   */
  nguoi_lh?: string | null;

  /**
   * Search index
   */
  xsearch?: string | null;

  /**
   * Sales representative (Foreign Key)
   */
  ma_nvbh?: string | null;

  /**
   * Account (Foreign Key)
   */
  tk?: string | null;

  /**
   * Payment terms (Foreign Key)
   */
  ma_tt?: string | null;

  /**
   * Payment method (Foreign Key)
   */
  ma_pttt?: string | null;

  /**
   * Supplier group 1 (Foreign Key)
   */
  nh_kh1?: string | null;

  /**
   * Supplier group 2 (Foreign Key)
   */
  nh_kh2?: string | null;

  /**
   * Supplier group 3 (Foreign Key)
   */
  nh_kh3?: string | null;

  /**
   * Region (Foreign Key)
   */
  rg_code?: string | null;

  /**
   * Phone
   */
  dien_thoai?: string | null;

  /**
   * Fax
   */
  fax?: string | null;

  /**
   * Email
   */
  e_mail?: string | null;

  /**
   * Website
   */
  home_page?: string | null;

  /**
   * Notes
   */
  ghi_chu?: string | null;

  /**
   * Credit limit
   */
  gioi_han_no?: number | null;

  /**
   * Birth date
   */
  ngay_sinh?: string | null;

  /**
   * ID number
   */
  cmnd_so?: string | null;

  /**
   * Legal representative
   */
  nguoi_dd_pl?: string | null;

  /**
   * Representative position
   */
  cv_nguoi_dd?: string | null;

  /**
   * Bank account
   */
  tknh?: string | null;

  /**
   * Bank name
   */
  ten_nh?: string | null;

  /**
   * Bank branch
   */
  chi_nhanh?: string | null;

  /**
   * Province/City
   */
  tinh_thanh?: string | null;

  /**
   * Description (additional)
   */
  mo_ta?: string | null;

  /**
   * Delivery address
   */
  dia_chi_giao_hang?: string | null;

  /**
   * Business field
   */
  linh_vuc_hoat_dong?: string | null;

  /**
   * Use e-invoice
   */
  sd_hddt?: string | null;

  /**
   * E-invoice email
   */
  e_mail_hddt?: string | null;

  /**
   * Representative
   */
  nguoi_dd?: string | null;

  /**
   * Additional info
   */
  additional_info?: any;

  /**
   * Reference to the entity model
   */
  entity_model?: string;
}
