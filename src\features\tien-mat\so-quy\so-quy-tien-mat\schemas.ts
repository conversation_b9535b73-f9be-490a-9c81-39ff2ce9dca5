import { z } from 'zod';

export const searchSchema = z.object({
  // Basic fields
  ngay_tu: z.string().optional(),
  ngay_den: z.string().optional(),
  tai_khoan: z.string().optional(),

  // Detail fields
  phan_loai: z.string().optional(),
  tu_so_ct: z.string().optional(),
  den_so_ct: z.string().optional(),

  // Other fields
  mau_bao_cao: z.string().optional(),
  chi_tiet_theo_doi_tuong: z.boolean().optional(),
  chi_tiet_theo_tk_doi_ung: z.boolean().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ngay_tu: '',
  ngay_den: '',
  tai_khoan: '',
  phan_loai: '',
  tu_so_ct: '',
  den_so_ct: '',
  mau_bao_cao: 'tien_chuan',
  chi_tiet_theo_doi_tuong: false,
  chi_tiet_theo_tk_doi_ung: false
};

// Legacy exports for compatibility
export const cashBookFilterSchema = searchSchema;
export const cashBookFilterInitialValues = initialValues;
