import { GridRowParams, GridRenderCellParams } from '@mui/x-data-grid';
import React, { useState, ReactElement } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { useSalesOrderStatusData } from './useSalesOrderStatusData';
import { SearchFormValues } from '../types';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: GridRowParams) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
}

export function useTableData(searchParams: SearchFormValues = {}): UseTableDataReturn {
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const { data, isLoading, error, refreshData } = useSalesOrderStatusData(searchParams);
  const handleRowClick = (params: GridRowParams) => {
    const order = params.row as any;
    setSelectedRowIndex(params.row.id.toString());
    console.log('Row clicked:', order);
  };

  const renderCellWithBold = (params: GridRenderCellParams): ReactElement => {
    const isSummaryRow = params.row.isSummary === true;
    return React.createElement('span', { className: isSummaryRow ? 'font-bold' : '' }, params.value);
  };

  const tables = [
    {
      name: 'Báo cáo tình trạng đơn hàng bán',
      rows: data,
      columns: [
        {
          field: 'unit_id',
          headerName: 'Đơn vị',
          width: 150
        },
        {
          field: 'ma_ngv',
          headerName: 'Loại chứng từ',
          width: 150
        },
        {
          field: 'ngay_ct',
          headerName: 'Ngày c/từ',
          width: 120
        },
        {
          field: 'so_ct',
          headerName: 'Số c/từ',
          width: 120
        },
        {
          field: 'ma_kh',
          headerName: 'Mã khách hàng',
          width: 150
        },
        {
          field: 'ten_kh',
          headerName: 'Tên khách hàng',
          width: 200,
          renderCell: renderCellWithBold
        },
        {
          field: 'ma_nvbh',
          headerName: 'Mã nhân viên bán hàng',
          width: 150
        },
        {
          field: 'ten_nvbh',
          headerName: 'Tên nhân viên bán hàng',
          width: 200
        },
        {
          field: 'status',
          headerName: 'Trạng thái',
          width: 120
        },
        {
          field: 'ma_vt',
          headerName: 'Mã vật tư',
          width: 150
        },
        {
          field: 'ten_vt',
          headerName: 'Tên vật tư',
          width: 200
        },
        {
          field: 'gia2',
          headerName: 'Đơn giá',
          width: 120
        },
        {
          field: 'tien2',
          headerName: 'Thành tiền',
          width: 120,
          renderCell: renderCellWithBold
        },
        {
          field: 'sl_dh',
          headerName: 'Sl đặt hàng',
          width: 150,
          renderCell: renderCellWithBold
        },
        {
          field: 'so_luong',
          headerName: 'Sl đặt đơn hàng',
          width: 150,
          renderCell: renderCellWithBold
        },
        {
          field: 'sl_xuat',
          headerName: 'Sl đã xuất',
          width: 150,
          renderCell: renderCellWithBold
        },
        {
          field: 'sl_hd',
          headerName: 'Sl hóa đơn',
          width: 150,
          renderCell: renderCellWithBold
        },
        {
          field: 'sl_tl',
          headerName: 'Sl trả lại',
          width: 150,
          renderCell: renderCellWithBold
        },
        {
          field: 'sl_cl',
          headerName: 'Sl còn lại',
          width: 150,
          renderCell: renderCellWithBold
        },
        {
          field: 'ma_bp',
          headerName: 'Bộ phận',
          width: 150
        },
        {
          field: 'ma_vv',
          headerName: 'Vụ việc',
          width: 150
        },
        {
          field: 'ma_hd',
          headerName: 'Hợp đồng',
          width: 150
        },
        {
          field: 'ma_dtt',
          headerName: 'Đợt thanh toán',
          width: 150
        },
        {
          field: 'ma_ku',
          headerName: 'Khế ước',
          width: 150
        },
        {
          field: 'ma_phi',
          headerName: 'Phí',
          width: 150
        },
        {
          field: 'ma_sp',
          headerName: 'Sản phẩm',
          width: 150
        },
        {
          field: 'ma_lsx',
          headerName: 'Lệnh sản xuất',
          width: 150
        },
        {
          field: 'ma_cp0',
          headerName: 'C/p không h/lệ',
          width: 150
        }
      ]
    }
  ];

  return {
    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData
  };
}
