import type { GridRenderCellParams } from '@mui/x-data-grid';
import type { ExtendedGridColDef } from '@/components/custom/arito';
import { Checkbox } from '@/components/ui/checkbox';
export * from './nhom';

export const vatTuSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_vt', headerName: 'Mã vật tư', width: 150 },
  { field: 'ten_vt', headerName: 'Tên vật tư', width: 200 },
  { field: 'dvt_data', headerName: 'Đvt', width: 80, renderCell: (params: any) => params.row.dvt_data?.dvt || '' },
  { field: 'nh_vt1', headerName: 'Nhóm 1', width: 120 },
  { field: 'lo_yn', headerName: 'Theo dõi lô', width: 90, checkboxSelection: true },
  { field: 'qc_yn', headerName: 'Quy cách', width: 90, checkboxSelection: true },
  { field: 'image_id', headerName: 'Hình ảnh', width: 120 }
];

export const vatTu1SearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    type: 'boolean',
    renderHeader: () => <Checkbox />,
    renderCell: (params: GridRenderCellParams) => <Checkbox checked={params.row.checkbox} />
  },
  { field: 'ma_vt', headerName: 'Mã vật tư', width: 150 },
  { field: 'ten_vt', headerName: 'Tên vật tư', width: 200 },
  { field: 'dvt_data', headerName: 'Đvt', width: 80, renderCell: (params: any) => params.row.dvt_data?.dvt || '' },
  { field: 'nh_vt1', headerName: 'Nhóm 1', width: 120 },
  { field: 'lo_yn', headerName: 'Theo dõi lô', width: 90, checkboxSelection: true },
  { field: 'qc_yn', headerName: 'Quy cách', width: 90, checkboxSelection: true },
  { field: 'image_id', headerName: 'Hình ảnh', width: 120 }
];

export const dvtSearchColumns: ExtendedGridColDef[] = [
  { field: 'dvt', headerName: 'Đvt', width: 80 },
  { field: 'ten_dvt', headerName: 'Tên đơn vị tính', width: 150 }
];

export const khachHangSearchColumns: ExtendedGridColDef[] = [
  { field: 'customer_code', headerName: 'Mã đối tượng', width: 150 },
  { field: 'customer_name', headerName: 'Tên đối tượng', width: 250 },
  { field: 'cong_no_phai_thu', headerName: 'Công nợ p/thu', width: 150 },
  { field: 'cong_no_phai_tra', headerName: 'Công nợ p/trả', width: 150 },
  { field: 'tax_code', headerName: 'Mã số thuế', width: 150 },
  { field: 'email', headerName: 'Email', width: 200 },
  { field: 'phone', headerName: 'Số điện thoại', width: 150 }
];

export const nhomKhachHangSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nh', headerName: 'Mã nhóm', width: 150 },
  { field: 'ten_nh', headerName: 'Tên nhóm', width: 250 }
];

export const ngoaiTeSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nt', headerName: 'Ngoại tệ', width: 80 },
  { field: 'ten_nt', headerName: 'Tên ngoại tệ', width: 150 }
];

export const loaiGiaBanSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_loai_gb', headerName: 'Mã loại giá', width: 150 },
  { field: 'ten_loai_gb', headerName: 'Tên loại giá', width: 250 }
];

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'code', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'name', headerName: 'Tên tài khoản', flex: 1 },
  {
    field: 'parent_account_code',
    headerName: 'Tài khoản mẹ',
    flex: 1,
    renderCell: (params: any) => params?.row?.parent_account_code_data?.code || ''
  },
  {
    field: 'tk_so_cai',
    headerName: 'Tk sổ cái',
    flex: 1,
    checkboxSelection: true
  },
  {
    field: 'tk_chi_tiet',
    headerName: 'Tk chi tiết',
    flex: 1,
    checkboxSelection: true
  },
  { field: 'bac_tk', headerName: 'Bậc tk', flex: 1 }
];

export const accountListSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    type: 'boolean',
    renderHeader: () => <Checkbox />,
    renderCell: (params: GridRenderCellParams) => {
      return <Checkbox checked={params.row.checkbox} />;
    }
  },
  { field: 'code', headerName: 'Mã tài khoản', width: 120 },
  { field: 'name', headerName: 'Tên tài khoản', width: 250 },
  {
    field: 'parent_account_code',
    headerName: 'Tài khoản mẹ',
    flex: 1,
    renderCell: (params: GridRenderCellParams) => {
      return <span>{params.row.parent_account_code_data?.code || ''}</span>;
    }
  },
  {
    field: 'tk_so_cai',
    headerName: 'Tk sổ cái',
    width: 100,
    type: 'boolean',
    renderCell: (params: GridRenderCellParams) => {
      return <Checkbox checked={params.row.tk_so_cai} disabled />;
    }
  },
  {
    field: 'tk_chi_tiet',
    headerName: 'Tk chi tiết',
    width: 100,
    type: 'boolean',
    renderCell: (params: GridRenderCellParams) => {
      return <Checkbox checked={params.row.tk_chi_tiet} disabled />;
    }
  },
  { field: 'bac_tk', headerName: 'Bậc tk', flex: 1 }
];

export const sanPhamSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_vt', headerName: 'Mã vật tư', width: 150 },
  { field: 'ten_vt', headerName: 'Tên vật tư', width: 250 },
  { field: 'dvt_data', headerName: 'Đvt', width: 80, renderCell: (params: any) => params.row.dvt_data?.dvt || '' }
];

export const lenhSanXuatSearchColumns: ExtendedGridColDef[] = [
  { field: 'so_lsx', headerName: 'Số lệnh', width: 150 },
  { field: 'description', headerName: 'Diễn giải', width: 250 }
];

export const lenhSanXuat1SearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    type: 'boolean',
    renderHeader: () => <Checkbox />,
    renderCell: (params: GridRenderCellParams) => <Checkbox checked={params.row.checkbox} />
  },
  { field: 'so_lsx', headerName: 'Số lệnh', width: 150 },
  { field: 'description', headerName: 'Diễn giải', width: 250 }
];

export const boPhanSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_bp', headerName: 'Mã bộ phận', width: 200 },
  { field: 'ten_bp', headerName: 'Tên bộ phận', width: 200 }
];

export const donViSearchColumns = [
  { field: 'ma_unit', headerName: 'Mã đơn vị', width: 120 },
  { field: 'ten_unit', headerName: 'Tên đơn vị', width: 250 }
];

export const bookSearchColumns = [
  { field: 'ma_quyen', headerName: 'Mã quyển', width: 150 },
  { field: 'ten_quyen', headerName: 'Tên quyển', width: 250 },
  { field: 'so_khai_bao', headerName: 'Số khai báo', width: 150 }
];

export const chungTuSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_ct', headerName: 'Mã chứng từ', width: 150 },
  { field: 'ten_ct', headerName: 'Tên chứng từ', width: 250 }
];

export const chungTu1SearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    type: 'boolean',
    renderHeader: () => <Checkbox />,
    renderCell: (params: GridRenderCellParams) => <Checkbox checked={params.row.checkbox} />
  },
  { field: 'ma_ct', headerName: 'Mã chứng từ', width: 150 },
  { field: 'ten_ct', headerName: 'Tên chứng từ', width: 250 }
];

export const quyenChungTuSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nk', headerName: 'Mã quyển', width: 150 },
  { field: 'ten_nk', headerName: 'Tên quyển', width: 250 },
  { field: 'mau_so', headerName: 'Mẫu số', width: 150 },
  { field: 'ky_hieu', headerName: 'Ký hiệu', width: 150 }
];

export const yeuToSearchColumns = [
  { field: 'ma_yt', headerName: 'Mã yếu tố', width: 150 },
  { field: 'ten_yt', headerName: 'Tên yếu tố', width: 250 }
];

export const taiSanSearchColumns = [
  { field: 'ma_ts', headerName: 'Mã tài sản', width: 150 },
  { field: 'ten_ts', headerName: 'Tên tài sản', width: 250 }
];

export const congDoanSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'ma_bp',
    headerName: 'Mã công đoạn',
    width: 150,
    renderCell: (params: any) => params.row.ma_bp_data?.ma_bp || ''
  },
  {
    field: 'ten_bp',
    headerName: 'Tên công đoạn',
    width: 250,
    renderCell: (params: any) => params.row.ma_bp_data?.ten_bp || ''
  }
];

export const congDoan1SearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    type: 'boolean',
    renderHeader: () => <Checkbox />,
    renderCell: (params: GridRenderCellParams) => <Checkbox checked={params.row.checkbox} />
  },
  {
    field: 'ma_bp',
    headerName: 'Mã công đoạn',
    width: 150,
    renderCell: (params: any) => params.row.ma_bp_data?.ma_bp || ''
  },
  {
    field: 'ten_bp',
    headerName: 'Tên công đoạn',
    width: 250,
    renderCell: (params: any) => params.row.ma_bp_data?.ten_bp || ''
  }
];

export const vuViecSearchColumns = [
  { field: 'ma_vv', headerName: 'Mã vụ việc', width: 150 },
  { field: 'ten_vv', headerName: 'Tên vụ việc', width: 250 }
];

export const phiSearchColumns = [
  { field: 'ma_phi', headerName: 'Mã phí', width: 150 },
  { field: 'ten_phi', headerName: 'Tên phí', width: 250 }
];

export const hopDongSearchColumns = [
  { field: 'ma_hd', headerName: 'Mã hợp đồng', width: 150 },
  { field: 'ten_hd', headerName: 'Tên hợp đồng', width: 250 }
];

export const objectListSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    type: 'boolean',
    renderHeader: () => <Checkbox />,
    renderCell: (params: GridRenderCellParams) => {
      return <Checkbox checked={params.row.customer_code} />;
    }
  },
  { field: 'customer_code', headerName: 'Mã', width: 150 },
  { field: 'customer_name', headerName: 'Tên', width: 250 }
];

export const groupColumns = [
  {
    field: 'ma_nhom',
    headerName: 'Mã nhóm',
    width: 150
  },
  {
    field: 'ten_phan_nhom',
    headerName: 'Tên nhóm',
    width: 250
  }
];

export const regionColumns = [
  {
    field: 'rg_code',
    headerName: 'Mã khu vực',
    width: 150
  },
  {
    field: 'rgname',
    headerName: 'Tên khu vực',
    width: 250
  }
];

export const customerSearchColumns: ExtendedGridColDef[] = [
  { field: 'customer_code', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'customer_name', headerName: 'Tên đối tượng', flex: 2 },
  { field: 'cong_no_phai_thu', headerName: 'Công nợ p/thu', flex: 1 },
  { field: 'cong_no_phai_tra', headerName: 'Công nợ p/trả', flex: 1 },
  { field: 'tax_code', headerName: 'Mã số thuế', flex: 1 },
  { field: 'email', headerName: 'Email', flex: 1 },
  { field: 'phone', headerName: 'Số điện thoại', flex: 1 }
];

export const congCuDungCuSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    sortable: false,
    disableColumnMenu: true,
    align: 'center',
    headerAlign: 'center',
    renderHeader: params => (
      <div className='flex h-full items-center justify-center'>
        <Checkbox onChange={e => {}} />
      </div>
    )
  },
  { field: 'ma_cc', headerName: 'Mã công cụ', flex: 1 },
  { field: 'ten_cc', headerName: 'Tên công cụ', flex: 1 }
];

export const loaiCongCuSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    sortable: false,
    disableColumnMenu: true,
    align: 'center',
    headerAlign: 'center',
    renderHeader: params => (
      <div className='flex h-full items-center justify-center'>
        <Checkbox onChange={e => {}} />
      </div>
    )
  },
  { field: 'id', headerName: 'Loại công cụ', flex: 1 },
  { field: 'name', headerName: 'Tên loại công cụ', flex: 1 }
];
export const donVi1SearchColumns: ExtendedGridColDef[] = [
  { field: 'checkbox', headerName: '', width: 50 },
  { field: 'ma_don_vi', headerName: 'Mã đơn vị', width: 100 },
  { field: 'ten_don_vi', headerName: 'Tên đơn vị', width: 200 },
  { field: 'id', headerName: 'ID', width: 80 }
];

export const loaiYeuToSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_loai', headerName: 'Mã loại yếu tố', width: 120 },
  { field: 'ten_loai', headerName: 'Tên loại yếu tố', width: 250 }
];

export const loaiTaiSanSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_lts', headerName: 'Loại tài sản', width: 150 },
  { field: 'ten_lts', headerName: 'Tên loại tài sản', width: 250 }
 
];

export const supplierSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_doi_tuong', headerName: 'Mã đối tượng', width: 100 },
  { field: 'ten_doi_tuong', headerName: 'Tên đối tượng', width: 280 },
  { field: 'cong_no_p_thu', headerName: 'Công nợ p/thu', width: 120 },
  { field: 'cong_no_p_tra', headerName: 'Công nợ p/trả', width: 120 },
  { field: 'ma_so_thue', headerName: 'Mã số thuế', width: 120 },
  { field: 'email', headerName: 'Email', width: 160 },
  { field: 'so_dien_thoai', headerName: 'Số điện thoại', width: 160 }
];

export const supplierGroupSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nhom', headerName: 'Mã nhóm', width: 120 },
  { field: 'ten_nhom', headerName: 'Tên nhóm', width: 280 }
];

export const regionSearchColumns: ExtendedGridColDef[] = [
  { field: 'name', headerName: 'Mã khu vực', flex: 1 },
  { field: 'region_name', headerName: 'Tên khu vực', flex: 2 }
];

export const dotThanhToanSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_dtt', headerName: 'Mã đợt', width: 100 },
  { field: 'ten_dtt', headerName: 'Tên đợt thanh toán', width: 250 },
  { field: 'ngay_tt', headerName: 'Ngày thanh toán', width: 120 },
  { field: 'ty_le', headerName: 'Tỷ lệ(%)', width: 120 },
  { field: 'tien', headerName: 'Tiền', width: 120 }
];

export const kheUocSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_ku', headerName: 'Mã khế ước', width: 100 },
  { field: 'ten_ku', headerName: 'Tên khế ước', width: 250 }
];

export const loaiVatTuSearchColumns: ExtendedGridColDef[] = [
  { field: 'loai_vat_tu', headerName: 'Loại vật tư', width: 120 },
  { field: 'ten_loai', headerName: 'Tên loại vật tư', width: 280 }
];

export const nhomVatTuSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nh', headerName: 'Mã nhóm', width: 120 },
  { field: 'ten_nh', headerName: 'Tên nhóm', width: 280 }
];

export const warehouseSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_kho', headerName: 'Mã kho', width: 120 },
  { field: 'ten_kho', headerName: 'Tên kho', width: 250 },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 120 },
  {
    field: 'vi_tri_yn',
    headerName: 'Theo dõi vị trí',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  }
];

export const giaoDichSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    sortable: false,
    disableColumnMenu: true
  },
  {
    field: 'transactionCode',
    headerName: 'Mã giao dịch',
    width: 120
  },
  {
    field: 'transactionName',
    headerName: 'Tên giao dịch',
    width: 200
  },
  {
    field: 'documentCode',
    headerName: 'Mã chứng từ',
    width: 120
  }
];

export const loSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'ma_lo',
    headerName: 'Mã lô',
    width: 120
  },
  {
    field: 'ten_lo',
    headerName: 'Tên lô',
    width: 250
  }
];

export const viTriSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'ma_vi_tri',
    headerName: 'Mã vị trí',
    width: 120
  },
  {
    field: 'ten_vi_tri',
    headerName: 'Tên vị trí',
    width: 250
  }
];

export const nhanVienSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nhan_vien', headerName: 'Mã nhân viên', flex: 1 },
  { field: 'ho_ten_nhan_vien', headerName: 'Tên nhân viên', flex: 2 }
];

export const nhomNhaCungCapSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nhom', headerName: 'Mã nhóm', flex: 1 },
  { field: 'ten_phan_nhom', headerName: 'Tên nhóm', flex: 2 }
];

export const khuVucSearchColumns: ExtendedGridColDef[] = [
  { field: 'rg_code', headerName: 'Mã khu vực', flex: 1 },
  { field: 'rgname', headerName: 'Tên khu vực', flex: 2 }
];
