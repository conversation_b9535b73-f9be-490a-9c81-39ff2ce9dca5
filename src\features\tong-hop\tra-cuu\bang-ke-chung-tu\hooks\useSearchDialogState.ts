import { useState } from 'react';
import {
  AccountModel,
  BoPhan,
  ChungTu,
  DotThanhToan,
  HopDong,
  KhachHang,
  KheUoc,
  NgoaiTe,
  Phi,
  VatTu,
  VuViec
} from '@/types/schemas';

export interface SearchDialogState {
  taiKhoan: AccountModel | null;
  taiKhoanDu: AccountModel | null;
  khachHang: KhachHang | null;
  ngoaiTe: NgoaiTe | null;
  chungTu: ChungTu | null;
  boPhan: BoPhan | null;
  vuViec: VuViec | null;
  hopDong: HopDong | null;
  dotThanhToan: DotThanhToan | null;
  kheUoc: KheUoc | null;
  phi: Phi | null;
  sanPham: VatTu | null;
  lenhSanXuat: any | null;
}

export interface SearchDialogActions {
  setTaiKhoan: (taiKhoan: AccountModel) => void;
  setTaiKhoanDu: (taiKhoanDu: AccountModel) => void;
  setKhachHang: (khachHang: KhachHang) => void;
  setNgoaiTe: (ngoaiTe: NgoaiTe) => void;
  setChungTu: (chungTu: ChungTu) => void;
  setBoPhan: (boPhan: BoPhan) => void;
  setVuViec: (vuViec: VuViec) => void;
  setHopDong: (hopDong: HopDong) => void;
  setDotThanhToan: (dotThanhToan: DotThanhToan) => void;
  setKheUoc: (kheUoc: KheUoc) => void;
  setPhi: (phi: Phi) => void;
  setSanPham: (sanPham: VatTu) => void;
  setLenhSanXuat: (lenhSanXuat: any) => void;
  resetState: () => void;
  updateState: (updates: Partial<SearchDialogState>) => void;
}

export interface UseSearchDialogStateReturn {
  state: SearchDialogState;
  actions: SearchDialogActions;
}

const initialState: SearchDialogState = {
  taiKhoan: null,
  taiKhoanDu: null,
  khachHang: null,
  ngoaiTe: null,
  chungTu: null,
  boPhan: null,
  vuViec: null,
  hopDong: null,
  dotThanhToan: null,
  kheUoc: null,
  phi: null,
  sanPham: null,
  lenhSanXuat: null
};

export function useSearchDialogState(initialValues?: Partial<SearchDialogState>): UseSearchDialogStateReturn {
  const [state, setState] = useState<SearchDialogState>({
    ...initialState,
    ...initialValues
  });

  const actions: SearchDialogActions = {
    setTaiKhoan: (taiKhoan: AccountModel) => {
      setState(prev => ({ ...prev, taiKhoan }));
    },

    setTaiKhoanDu: (taiKhoanDu: AccountModel) => {
      setState(prev => ({ ...prev, taiKhoanDu }));
    },

    setKhachHang: (khachHang: KhachHang) => {
      setState(prev => ({ ...prev, khachHang }));
    },

    setNgoaiTe: (ngoaiTe: NgoaiTe) => {
      setState(prev => ({ ...prev, ngoaiTe }));
    },

    setChungTu: (chungTu: ChungTu) => {
      setState(prev => ({ ...prev, chungTu }));
    },

    setBoPhan: (boPhan: BoPhan) => {
      setState(prev => ({ ...prev, boPhan }));
    },

    setVuViec: (vuViec: VuViec) => {
      setState(prev => ({ ...prev, vuViec }));
    },

    setHopDong: (hopDong: HopDong) => {
      setState(prev => ({ ...prev, hopDong }));
    },

    setDotThanhToan: (dotThanhToan: DotThanhToan) => {
      setState(prev => ({ ...prev, dotThanhToan }));
    },

    setKheUoc: (kheUoc: KheUoc) => {
      setState(prev => ({ ...prev, kheUoc }));
    },

    setPhi: (phi: Phi) => {
      setState(prev => ({ ...prev, phi }));
    },

    setSanPham: (sanPham: VatTu) => {
      setState(prev => ({ ...prev, sanPham }));
    },

    setLenhSanXuat: (lenhSanXuat: any) => {
      setState(prev => ({ ...prev, lenhSanXuat }));
    },

    resetState: () => {
      setState(initialState);
    },

    updateState: (updates: Partial<SearchDialogState>) => {
      setState(prev => ({ ...prev, ...updates }));
    }
  };

  return { state, actions };
}
