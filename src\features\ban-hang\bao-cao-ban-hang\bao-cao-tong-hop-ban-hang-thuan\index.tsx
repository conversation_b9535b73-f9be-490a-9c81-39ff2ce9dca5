'use client';

import { useState } from 'react';
import { EditPrintTemplateDialog } from '@/components/custom/arito/edit-print-template';
import { useDialogState, useTableData, useActionHandlers } from './hooks';
import AritoDataTables from '@/components/custom/arito/data-tables';
import InitialSearchDialog from './components/InitialSearchDialog';
import { LoadingOverlay } from '@/components/custom/arito';
import { useBaoCaoTongHopBanHangThuan } from '@/hooks';
import ActionBar from './components/ActionBar';

export default function DebtBalanceReportPage() {
  const {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate
  } = useDialogState();

  const [searchParams, setSearchParams] = useState<any>({});
  const { data, isLoading, error, fetchData, refreshData } = useBaoCaoTongHopBanHangThuan(searchParams);
  const { tables, handleRowClick } = useTableData(data);
  const { handleRefreshClick, handleFixedColumnsClick, handleExportDataClick } = useActionHandlers();

  const handleSearchWithData = async (values: any) => {
    setSearchParams(values);
    await fetchData(values);
    handleInitialSearch(values);
  };

  const handleRefreshWithData = async () => {
    await refreshData();
    handleRefreshClick();
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleSearchWithData}
      />

      <EditPrintTemplateDialog
        open={editPrintTemplateDialogOpen}
        onClose={handleClosePrintTemplateDialog}
        onSave={handleSavePrintTemplate}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshWithData}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            className='border-b border-gray-200'
            fromDate={searchParams.ngay_ct1}
            toDate={searchParams.ngay_ct2}
          />

          <div className='flex-1 overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && <AritoDataTables tables={tables} onRowClick={handleRowClick} />}
          </div>
        </>
      )}
    </div>
  );
}
