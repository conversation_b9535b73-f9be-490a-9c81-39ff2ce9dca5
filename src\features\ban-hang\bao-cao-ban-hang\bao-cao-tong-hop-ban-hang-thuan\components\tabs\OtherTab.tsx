import { useState } from 'react';

import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { DocumentNumberRangeField } from '@/components/custom/arito/form/search-fields';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import SaveTemplateDialog, { SaveTemplateFormData } from '../SaveTemplateDialog';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

const OtherTab: React.FC = () => {
  const [saveFilterTemplateDialogOpen, setSaveFilterTemplateDialogOpen] = useState(false);

  const handleSaveFilterTemplate = (data: SaveTemplateFormData) => {
    console.log('Saving filter template:', data);
    setSaveFilterTemplateDialogOpen(false);
  };

  return (
    <div className='space-y-2 p-4' style={{ width: '800px', minWidth: '800px' }}>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã giao dịch:</Label>
          <div className='w-96'>
            <SearchField
              type='text'
              displayRelatedField='transaction_name'
              columnDisplay='transaction_code'
              searchEndpoint={`/${QUERY_KEYS.GIAO_DICH}/`}
              searchColumns={[
                { field: 'transaction_code', headerName: 'Mã giao dịch', width: 120 },
                { field: 'transaction_name', headerName: 'Tên giao dịch', width: 200 },
                { field: 'document_code', headerName: 'Mã chứng từ', width: 120 }
              ]}
              dialogTitle='Danh mục giao dịch'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản vật tư:</Label>
          <div className='flex-1'>
            <SearchField
              type='text'
              displayRelatedField='name'
              columnDisplay='code'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={[
                { field: 'code', headerName: 'Mã tài khoản', width: 120 },
                { field: 'name', headerName: 'Tên tài khoản', width: 200 },
                {
                  field: 'parent_account_code',
                  headerName: 'Tài khoản mẹ',
                  width: 120,
                  renderCell: (params: any) => {
                    return params.row.parent_account_code_data?.code || '';
                  }
                },
                { field: 'bac_tk', headerName: 'Bậc tk', width: 80 }
              ]}
              dialogTitle='Danh mục tài khoản'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản doanh thu:</Label>
          <div className='flex-1'>
            <SearchField
              type='text'
              displayRelatedField='name'
              columnDisplay='code'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={[
                { field: 'code', headerName: 'Mã tài khoản', width: 120 },
                { field: 'name', headerName: 'Tên tài khoản', width: 200 },
                {
                  field: 'parent_account_code',
                  headerName: 'Tài khoản mẹ',
                  width: 120,
                  renderCell: (params: any) => {
                    return params.row.parent_account_code_data?.code || '';
                  }
                },
                { field: 'bac_tk', headerName: 'Bậc tk', width: 80 }
              ]}
              dialogTitle='Danh mục tài khoản'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản giá vốn:</Label>
          <div className='flex-1'>
            <SearchField
              type='text'
              displayRelatedField='name'
              columnDisplay='code'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={[
                { field: 'code', headerName: 'Mã tài khoản', width: 120 },
                { field: 'name', headerName: 'Tên tài khoản', width: 200 },
                {
                  field: 'parent_account_code',
                  headerName: 'Tài khoản mẹ',
                  width: 120,
                  renderCell: (params: any) => {
                    return params.row.parent_account_code_data?.code || '';
                  }
                },
                { field: 'bac_tk', headerName: 'Bậc tk', width: 80 }
              ]}
              dialogTitle='Danh mục tài khoản'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã lô:</Label>
          <div className='flex-1'>
            <SearchField
              type='text'
              displayRelatedField='batch_name'
              columnDisplay='batch_code'
              searchEndpoint={`/${QUERY_KEYS.LO}/`}
              searchColumns={[
                { field: 'batch_code', headerName: 'Mã lô', width: 120 },
                { field: 'batch_name', headerName: 'Tên lô', width: 200 }
              ]}
              dialogTitle='Danh mục lô'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã vị trí:</Label>
          <div className='flex-1'>
            <SearchField
              type='text'
              displayRelatedField='location_name'
              columnDisplay='location_code'
              searchEndpoint={`/${QUERY_KEYS.VI_TRI}/`}
              searchColumns={[
                { field: 'location_code', headerName: 'Mã vị trí', width: 120 },
                { field: 'location_name', headerName: 'Tên vị trí', width: 200 }
              ]}
              dialogTitle='Danh mục vị trí'
            />
          </div>
        </div>
        <DocumentNumberRangeField formMode={'view'} label='Số c/từ (từ/đến)' />
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Diễn giải:</Label>
          <div className='flex-1'>
            <FormField name='description' label='' type='text' className='w-96' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu lọc báo cáo:</Label>
          <div className='flex items-center gap-1'>
            <FormField
              name='reportFilterTemplate'
              label=''
              type='select'
              options={[{ value: 'user_filter', label: 'Người dùng tự lọc' }]}
              className='w-96'
            />

            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={624}
                items={[
                  {
                    value: 'save_new',
                    label: 'Lưu mẫu mới',
                    icon: 7,
                    onClick: () => setSaveFilterTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current filter template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Save Filter Template Dialog */}
      <SaveTemplateDialog
        open={saveFilterTemplateDialogOpen}
        onClose={() => setSaveFilterTemplateDialogOpen(false)}
        onSave={handleSaveFilterTemplate}
        templateType='filter'
      />
    </div>
  );
};

export default OtherTab;
