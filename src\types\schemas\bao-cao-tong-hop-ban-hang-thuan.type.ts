// TypeScript interfaces for BaoCaoTongHopBanHangThuan (Sales Summary Report)

export interface BaoCaoTongHopBanHangThuanItem {
  id: string;
  ma_vt: string;
  ten_vt: string;
  dvt: string;
  tien_ban: number;
  tien_tl: number;
  tien_cl: number;
}

export interface BaoCaoTongHopBanHangThuanResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BaoCaoTongHopBanHangThuanItem[];
}

export interface BaoCaoTongHopBanHangThuanSearchFormValues {
  ngay_ct1?: string;
  ngay_ct2?: string;
  ma_kh?: string;
  nh_kh1?: string;
  nh_kh2?: string;
  nh_kh3?: string;
  rg_code?: string;
  ma_vt?: string;
  nh_vt1?: string;
  nh_vt2?: string;
  nh_vt3?: string;
  ma_kho?: string;
  ma_unit?: string;
  ma_gd?: string;
  so_ct1?: string;
  so_ct2?: string;
  dien_giai?: string;
  ton_kho_yn?: boolean;
  nh_ct?: string;
  loai_du_lieu?: number;
  mau_bc?: string;
  [key: string]: any;
}

export interface UseBaoCaoTongHopBanHangThuanReturn {
  data: BaoCaoTongHopBanHangThuanItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: BaoCaoTongHopBanHangThuanSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
