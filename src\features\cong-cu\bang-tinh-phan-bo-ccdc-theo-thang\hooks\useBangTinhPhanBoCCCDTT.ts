import { useState, useCallback } from 'react';
import { BangTinhPhanBoCCDCTTItem, BangTinhPhanBoCCDCTTResponse, UseBangTinhPhanBoCCDCTTReturn } from '@/types/schemas';
import { SearchFormValues } from '../schema';
import api from '@/lib/api';

export type BangTinhPhanBoCCDCTTSearchFormValues = SearchFormValues;

const generateMockData = (searchParams?: any): BangTinhPhanBoCCDCTTItem[] => {
  const dynamicMonthColumn =
    searchParams?.den_ky && searchParams?.den_nam
      ? `${String(searchParams.den_ky).padStart(2, '0')}${searchParams.den_nam}${String(searchParams.den_ky).padStart(2, '0')}`
      : '04202505';
  return [
    {
      id: '1',
      ma_cc: 'CC001',
      ten_cc: '<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> cầm ta<PERSON>',
      ma_lcc: 'LCC001',
      ten_lcc: '<PERSON><PERSON>ng cụ cầm tay',
      ten_dvt: 'Chiếc',
      so_luong: 1,
      ngay_kh0: '2025-01-15',
      so_ky_kh: 36,
      ngay_kh_kt: '2028-01-15',
      tk_cc: '242',
      ma_bp: 'BP001',
      systotal: 2500000,
      '01': 2500000,
      '02': 500000,
      '03': 2000000,
      [dynamicMonthColumn]: 69444,
      '05': 69444,
      '06': 569444,
      '07': 1930556
    },
    {
      id: '2',
      ma_cc: 'CC002',
      ten_cc: 'Bộ dụng cụ sửa chữa điện tử',
      ma_lcc: 'LCC001',
      ten_lcc: 'Dụng cụ cầm tay',
      ten_dvt: 'Bộ',
      so_luong: 2,
      ngay_kh0: '2025-01-16',
      so_ky_kh: 24,
      ngay_kh_kt: '2027-01-16',
      tk_cc: '242',
      ma_bp: 'BP001',
      systotal: 850000,
      '01': 850000,
      '02': 170000,
      '03': 680000,
      [dynamicMonthColumn]: 35417,
      '05': 35417,
      '06': 205417,
      '07': 644583
    },
    {
      id: '3',
      ma_cc: 'CC003',
      ten_cc: 'Máy hàn điện tử Inverter',
      ma_lcc: 'LCC002',
      ten_lcc: 'Thiết bị hàn',
      ten_dvt: 'Máy',
      so_luong: 1,
      ngay_kh0: '2025-01-20',
      so_ky_kh: 60,
      ngay_kh_kt: '2030-01-20',
      tk_cc: '242',
      ma_bp: 'BP001',
      systotal: 15000000,
      '01': 15000000,
      '02': 1250000,
      '03': 13750000,
      [dynamicMonthColumn]: 250000,
      '05': 250000,
      '06': 1500000,
      '07': 13500000
    },
    {
      id: '4',
      ma_cc: 'CC004',
      ten_cc: 'Bộ dụng cụ đo lường chính xác',
      ma_lcc: 'LCC003',
      ten_lcc: 'Dụng cụ đo lường',
      ten_dvt: 'Bộ',
      so_luong: 5,
      ngay_kh0: '2025-02-01',
      so_ky_kh: 120,
      ngay_kh_kt: '2035-02-01',
      tk_cc: '242',
      ma_bp: 'BP002',
      systotal: 1200000,
      '01': 1200000,
      '02': 120000,
      '03': 1080000,
      [dynamicMonthColumn]: 10000,
      '05': 10000,
      '06': 130000,
      '07': 1070000
    },
    {
      id: '5',
      ma_cc: 'CC005',
      ten_cc: 'Máy cắt kim loại cầm tay',
      ma_lcc: 'LCC002',
      ten_lcc: 'Thiết bị cắt',
      ten_dvt: 'Máy',
      so_luong: 1,
      ngay_kh0: '2025-02-15',
      so_ky_kh: 84,
      ngay_kh_kt: '2032-02-15',
      tk_cc: '242',
      ma_bp: 'BP003',
      systotal: 4500000,
      '01': 4500000,
      '02': 535714,
      '03': 3964286,
      [dynamicMonthColumn]: 53571,
      '05': 53571,
      '06': 589285,
      '07': 3910715
    }
  ];
};

/**
 * Custom hook for managing BangTinhPhanBoCCDCTT (Monthly Tool Equipment Allocation Calculation) data
 *
 * This hook provides functionality to fetch monthly tool equipment allocation calculation data
 * with mock support for testing and development purposes.
 */
export function useBangTinhPhanBoCCDCTT(
  searchParams: BangTinhPhanBoCCDCTTSearchFormValues
): UseBangTinhPhanBoCCDCTTReturn {
  const [data, setData] = useState<BangTinhPhanBoCCDCTTItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BangTinhPhanBoCCDCTTSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData(searchParams);

      const response = await api.get<BangTinhPhanBoCCDCTTResponse>(
        '/cong-cu/phan-bo-cong-cu-dung-cu/bang-tinh-phan-bo-ccdc-theo-thang/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
