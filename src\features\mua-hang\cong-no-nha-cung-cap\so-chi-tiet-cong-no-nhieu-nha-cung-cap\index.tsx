'use client';

import React from 'react';
import { ActionBar, EditPrintTemplateDialog, InitialSearchDialog } from './components';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { useDialogState, useTableData, useActionHandlers } from './hooks';

export default function MultiVendorDebtDetailLedgerPage() {
  const {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate
  } = useDialogState();

  const { tables, handleRowClick, isLoading, error, refreshData } = useTableData(searchParams);

  const { handleRefreshClick, handleFixedColumnsClick, handleExportDataClick } = useActionHandlers(refreshData);

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleInitialSearch}
      />

      <EditPrintTemplateDialog
        open={editPrintTemplateDialogOpen}
        onClose={handleClosePrintTemplateDialog}
        onSave={handleSavePrintTemplate}
      />

      {isLoading && <LoadingOverlay />}

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            className='border-b border-gray-200'
            searchParams={searchParams}
          />

          <div className='flex-1 overflow-hidden'>
            <AritoDataTables tables={tables} onRowClick={handleRowClick} />
          </div>
        </>
      )}
    </div>
  );
}
