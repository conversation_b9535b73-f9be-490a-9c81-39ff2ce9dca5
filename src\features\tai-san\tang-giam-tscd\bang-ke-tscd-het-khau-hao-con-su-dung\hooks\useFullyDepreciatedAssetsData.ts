import { useState, useCallback, useEffect } from 'react';
import { SearchFormValues } from '../schema';
import api from '@/lib/api';

export interface FullyDepreciatedAssetItem {
  id: string;
  stt: number;
  ma_tai_san: string;
  ten_tai_san: string;
  nguyen_gia: number;
  gt_da_khau_hao: number;
  ngay_mua: Date;
  ngay_tinh_kh: Date;
  so_ky_khau_hao: number;
  ngay_kt_kh: Date;
}

export interface FullyDepreciatedAssetsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: FullyDepreciatedAssetItem[];
}

export interface UseFullyDepreciatedAssetsReturn {
  data: FullyDepreciatedAssetItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}

const generateMockData = (): FullyDepreciatedAssetItem[] => {
  return [
    {
      id: '1',
      stt: 1,
      ma_tai_san: 'TS001',
      ten_tai_san: '<PERSON><PERSON><PERSON> <PERSON><PERSON>h Dell Inspiron 15 (Hết khấu hao)',
      nguyen_gia: 15000000,
      gt_da_khau_hao: 15000000,
      ngay_mua: new Date(2019, 0, 1),
      ngay_tinh_kh: new Date(2019, 1, 1),
      so_ky_khau_hao: 36,
      ngay_kt_kh: new Date(2022, 0, 1)
    },
    {
      id: '2',
      stt: 2,
      ma_tai_san: 'TS002',
      ten_tai_san: 'Máy in HP LaserJet Pro (Hết khấu hao)',
      nguyen_gia: 8000000,
      gt_da_khau_hao: 8000000,
      ngay_mua: new Date(2018, 2, 15),
      ngay_tinh_kh: new Date(2018, 3, 1),
      so_ky_khau_hao: 60,
      ngay_kt_kh: new Date(2023, 3, 1)
    },
    {
      id: '3',
      stt: 3,
      ma_tai_san: 'TS003',
      ten_tai_san: 'Bàn làm việc gỗ cao cấp (Hết khấu hao)',
      nguyen_gia: 5000000,
      gt_da_khau_hao: 5000000,
      ngay_mua: new Date(2017, 11, 5),
      ngay_tinh_kh: new Date(2018, 0, 1),
      so_ky_khau_hao: 120,
      ngay_kt_kh: new Date(2027, 11, 31)
    },
    {
      id: '4',
      stt: 4,
      ma_tai_san: 'TS004',
      ten_tai_san: 'Máy điều hòa Daikin 2HP (Hết khấu hao)',
      nguyen_gia: 12000000,
      gt_da_khau_hao: 12000000,
      ngay_mua: new Date(2018, 7, 20),
      ngay_tinh_kh: new Date(2018, 8, 1),
      so_ky_khau_hao: 120,
      ngay_kt_kh: new Date(2028, 7, 31)
    },
    {
      id: '5',
      stt: 5,
      ma_tai_san: 'TS005',
      ten_tai_san: 'Tủ sắt văn phòng (Hết khấu hao)',
      nguyen_gia: 3000000,
      gt_da_khau_hao: 3000000,
      ngay_mua: new Date(2016, 5, 10),
      ngay_tinh_kh: new Date(2016, 6, 1),
      so_ky_khau_hao: 120,
      ngay_kt_kh: new Date(2026, 5, 30)
    },
    {
      id: '6',
      stt: 6,
      ma_tai_san: 'TS006',
      ten_tai_san: 'Máy photocopy Canon (Hết khấu hao)',
      nguyen_gia: 25000000,
      gt_da_khau_hao: 25000000,
      ngay_mua: new Date(2017, 3, 15),
      ngay_tinh_kh: new Date(2017, 4, 1),
      so_ky_khau_hao: 84,
      ngay_kt_kh: new Date(2024, 4, 1)
    }
  ];
};

/**
 * Custom hook for managing Fully Depreciated Assets data
 *
 * This hook provides functionality to fetch fully depreciated assets data
 * with mock support for testing and development purposes.
 */
export function useFullyDepreciatedAssetsData(searchParams: SearchFormValues): UseFullyDepreciatedAssetsReturn {
  const [data, setData] = useState<FullyDepreciatedAssetItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<FullyDepreciatedAssetsResponse>(
        '/tai-san/tang-giam-tscd/bang-ke-tscd-het-khau-hao-con-su-dung/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
