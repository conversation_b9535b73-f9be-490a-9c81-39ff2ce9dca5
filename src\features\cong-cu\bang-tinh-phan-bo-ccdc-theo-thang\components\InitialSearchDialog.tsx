import React, { useState } from 'react';

import { Button } from '@mui/material';

import { AritoDialog, AritoForm, AritoHeaderTabs, AritoIcon, BottomBar } from '@/components/custom/arito';
import { DetailsTab, BasicInfo, OtherTab } from './tabs';
import { searchSchema, initialValues } from '../schema';
import { useSearchFieldStates } from '../hooks';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const searchFieldStates = useSearchFieldStates();
  const { taiSan, loaiTaiSan, boPhan, nhom1, nhom2, nhom3, setTaiSan } = searchFieldStates;

  const handleSubmit = (data: any) => {
    const combinedData = {
      ...data,
      ma_cc: taiSan?.uuid || '',
      ma_lcc: loaiTaiSan?.uuid || '',
      ma_bp: boPhan?.uuid || '',
      nh_cc1: nhom1?.uuid || '',
      nh_cc2: nhom2?.uuid || '',
      nh_cc3: nhom3?.uuid || ''
    };
    onSearch(combinedData);
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Bảng tính phân bổ CCDC theo tháng'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={data => {
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] min-w-[900px] overflow-y-auto'>
            <BasicInfo searchFieldStates={{ taiSan, setTaiSan }} />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailsTab searchFieldStates={searchFieldStates} />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
        bottomBar={<BottomBar mode='add' onClose={onClose} />}
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
