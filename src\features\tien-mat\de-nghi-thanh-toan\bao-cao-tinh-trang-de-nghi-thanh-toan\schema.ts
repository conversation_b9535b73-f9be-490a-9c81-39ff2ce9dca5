import { z } from 'zod';

// Search form schema for validation
export const searchFormSchema = z.object({
  // Date range fields from BasicInfoTab
  ngay_ct1: z.date(),
  ngay_ct2: z.date(),
  so_ct1: z.string().optional(),
  so_ct2: z.string().optional(),

  // Fields from GeneralInfoTab (SearchField excluded as per requirements)
  status: z.string().default('10'),
  mau_bc: z.string().default('20'),

  // Fields from OtherTab
  dien_giai: z.string().optional(),
  report_filtering: z.string().default('0')
});

export type SearchFormValues = z.infer<typeof searchFormSchema>;

// Re-export types for convenience
export type {
  BaoCaoTinhTrangDeNghiThanhToanItem,
  BaoCaoTinhTrangDeNghiThanhToanResponse,
  UseBaoCaoTinhTrangDeNghiThanhToanReturn
} from '@/types/schemas/bao-cao-tinh-trang-de-nghi-thanh-toan.type';

export const initialSearchValues: SearchFormValues = {
  ngay_ct1: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
  ngay_ct2: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
  so_ct1: '',
  so_ct2: '',
  status: '10',
  mau_bc: '20',
  dien_giai: '',
  report_filtering: '0'
};
