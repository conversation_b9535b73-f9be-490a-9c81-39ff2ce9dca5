import { useState, useCallback, useEffect } from 'react';
import { SalesOrderStatusItem, SalesOrderStatusResponse, SearchFormValues, UseSalesOrderStatusReturn } from '../types';
import api from '@/lib/api';

const generateMockData = (): SalesOrderStatusItem[] => {
  // Regular data rows
  const mockData: SalesOrderStatusItem[] = [
    {
      // System fields
      syspivot: '',
      sysorder: 1,
      sysprint: '',
      systotal: '',

      // Core fields
      id: '1',
      line: 1,
      unit_id: 'CN001',

      // Entity codes
      ma_ngv: 'DBH',
      ma_kh: 'KH001',
      ma_nvbh: 'NV001',
      ma_ct: 'DBH',
      ma_vt: 'SP001',
      ma_bp: 'BP001',
      ma_cp0: 0,
      ma_dtt: 'DTT001',
      ma_hd: 'HD2024001',
      ma_ku: 'KU2024001',
      ma_lsx: 'LSX2024001',
      ma_phi: 10000000,
      ma_sp: 'SP001',
      ma_mr1: 'MR001',
      ma_mr2: 'MR002',
      ma_mr3: 'MR003',
      ma_vv: 'VV001',
      ma_unit: 'CN001',

      // Document fields
      ngay_ct: '2024-01-15',
      so_ct: 'DBH2024001',
      status: 'Đang thực hiện',
      dvt: 'Cái',
      he_so: 1,
      ngay_giao: '2024-02-15',

      // Transaction fields
      gia2: 500000000,
      tien2: 1000000000,
      so_luong: 2,
      sl_xuat: 1,
      sl_hd: 1,
      sl_dh: 2,
      sl_tl: 0,
      sl_cl: 1,

      // Display names
      ten_ngv: 'Đơn bán hàng',
      ten_kh: 'Công ty TNHH Xây dựng Minh Phát',
      ten_vt: 'Máy ép thủy lực 100T',
      ten_ttct: 'Đang thực hiện',
      ten_nvbh: 'Nguyễn Văn A'
    },
    {
      // System fields
      syspivot: '',
      sysorder: 2,
      sysprint: '',
      systotal: '',

      // Core fields
      id: '2',
      line: 2,
      unit_id: 'CN001',

      // Entity codes
      ma_ngv: 'DBH',
      ma_kh: 'KH002',
      ma_nvbh: 'NV002',
      ma_ct: 'DBH',
      ma_vt: 'SP002',
      ma_bp: 'BP002',
      ma_cp0: 0,
      ma_dtt: 'DTT002',
      ma_hd: 'HD2024002',
      ma_ku: 'KU2024002',
      ma_lsx: 'LSX2024002',
      ma_phi: 3000000,
      ma_sp: 'SP002',
      ma_mr1: 'MR004',
      ma_mr2: 'MR005',
      ma_mr3: 'MR006',
      ma_vv: 'VV002',
      ma_unit: 'CN001',

      // Document fields
      ngay_ct: '2024-01-16',
      so_ct: 'DBH2024002',
      status: 'Hoàn thành',
      dvt: 'Cái',
      he_so: 1,
      ngay_giao: '2024-02-16',

      // Transaction fields
      gia2: 15000000,
      tien2: 150000000,
      so_luong: 10,
      sl_xuat: 10,
      sl_hd: 10,
      sl_dh: 10,
      sl_tl: 0,
      sl_cl: 0,

      // Display names
      ten_ngv: 'Đơn bán hàng',
      ten_kh: 'Công ty CP Cơ khí Hà Nội',
      ten_vt: 'Động cơ điện 5HP',
      ten_ttct: 'Hoàn thành',
      ten_nvbh: 'Trần Thị B'
    },
    {
      // System fields
      syspivot: '',
      sysorder: 3,
      sysprint: '',
      systotal: '',

      // Core fields
      id: '3',
      line: 3,
      unit_id: 'CN002',

      // Entity codes
      ma_ngv: 'DBH',
      ma_kh: 'KH003',
      ma_nvbh: 'NV003',
      ma_ct: 'DBH',
      ma_vt: 'SP003',
      ma_bp: 'BP003',
      ma_cp0: 0,
      ma_dtt: 'DTT003',
      ma_hd: 'HD2024003',
      ma_ku: 'KU2024003',
      ma_lsx: 'LSX2024003',
      ma_phi: 4800000,
      ma_sp: 'SP003',
      ma_mr1: 'MR007',
      ma_mr2: 'MR008',
      ma_mr3: 'MR009',
      ma_vv: 'VV003',
      ma_unit: 'CN002',

      // Document fields
      ngay_ct: '2024-01-17',
      so_ct: 'DBH2024003',
      status: 'Chờ duyệt',
      dvt: 'Bộ',
      he_so: 1,
      ngay_giao: '2024-02-17',

      // Transaction fields
      gia2: 80000000,
      tien2: 240000000,
      so_luong: 3,
      sl_xuat: 0,
      sl_hd: 0,
      sl_dh: 3,
      sl_tl: 0,
      sl_cl: 3,

      // Display names
      ten_ngv: 'Đơn bán hàng',
      ten_kh: 'Công ty TNHH Điện lạnh Miền Nam',
      ten_vt: 'Tủ điều khiển PLC',
      ten_ttct: 'Chờ duyệt',
      ten_nvbh: 'Lê Văn C'
    },
    {
      // System fields
      syspivot: '',
      sysorder: 4,
      sysprint: '',
      systotal: '',

      // Core fields
      id: '4',
      line: 4,
      unit_id: 'CN001',

      // Entity codes
      ma_ngv: 'DBH',
      ma_kh: 'KH004',
      ma_nvbh: 'NV001',
      ma_ct: 'DBH',
      ma_vt: 'SP004',
      ma_bp: 'BP001',
      ma_cp0: 0,
      ma_dtt: 'DTT004',
      ma_hd: 'HD2024004',
      ma_ku: 'KU2024004',
      ma_lsx: 'LSX2024004',
      ma_phi: 6000000,
      ma_sp: 'SP004',
      ma_mr1: 'MR010',
      ma_mr2: 'MR011',
      ma_mr3: 'MR012',
      ma_vv: 'VV004',
      ma_unit: 'CN001',

      // Document fields
      ngay_ct: '2024-01-18',
      so_ct: 'DBH2024004',
      status: 'Đang thực hiện',
      dvt: 'Cái',
      he_so: 1,
      ngay_giao: '2024-02-18',

      // Transaction fields
      gia2: 300000000,
      tien2: 300000000,
      so_luong: 1,
      sl_xuat: 0,
      sl_hd: 0,
      sl_dh: 1,
      sl_tl: 0,
      sl_cl: 1,

      // Display names
      ten_ngv: 'Đơn bán hàng',
      ten_kh: 'Công ty CP Thép Việt Đức',
      ten_vt: 'Máy cắt plasma CNC',
      ten_ttct: 'Đang thực hiện',
      ten_nvbh: 'Nguyễn Văn A'
    },
    {
      // System fields
      syspivot: '',
      sysorder: 5,
      sysprint: '',
      systotal: '',

      // Core fields
      id: '5',
      line: 5,
      unit_id: 'CN003',

      // Entity codes
      ma_ngv: 'DBH',
      ma_kh: 'KH005',
      ma_nvbh: 'NV004',
      ma_ct: 'DBH',
      ma_vt: 'SP005',
      ma_bp: 'BP004',
      ma_cp0: 0,
      ma_dtt: 'DTT005',
      ma_hd: 'HD2024005',
      ma_ku: 'KU2024005',
      ma_lsx: 'LSX2024005',
      ma_phi: 18000000,
      ma_sp: 'SP005',
      ma_mr1: 'MR013',
      ma_mr2: 'MR014',
      ma_mr3: 'MR015',
      ma_vv: 'VV005',
      ma_unit: 'CN003',

      // Document fields
      ngay_ct: '2024-01-19',
      so_ct: 'DBH2024005',
      status: 'Lập chứng từ',
      dvt: 'Bộ',
      he_so: 1,
      ngay_giao: '2024-02-19',

      // Transaction fields
      gia2: 450000000,
      tien2: 900000000,
      so_luong: 2,
      sl_xuat: 0,
      sl_hd: 0,
      sl_dh: 2,
      sl_tl: 0,
      sl_cl: 2,

      // Display names
      ten_ngv: 'Đơn bán hàng',
      ten_kh: 'Công ty TNHH Ô tô Thành Công',
      ten_vt: 'Hệ thống băng tải tự động',
      ten_ttct: 'Lập chứng từ',
      ten_nvbh: 'Phạm Văn D'
    }
  ];

  // Calculate totals for summary row
  const totalTien2 = mockData.reduce((sum, item) => sum + item.tien2, 0);
  const totalSoLuong = mockData.reduce((sum, item) => sum + item.so_luong, 0);
  const totalSlXuat = mockData.reduce((sum, item) => sum + item.sl_xuat, 0);
  const totalSlHd = mockData.reduce((sum, item) => sum + item.sl_hd, 0);
  const totalSlDh = mockData.reduce((sum, item) => sum + item.sl_dh, 0);
  const totalSlTl = mockData.reduce((sum, item) => sum + item.sl_tl, 0);
  const totalSlCl = mockData.reduce((sum, item) => sum + item.sl_cl, 0);

  // Summary row with "Tổng cộng" in bold
  const summaryRow: SalesOrderStatusItem = {
    // System fields
    syspivot: '',
    sysorder: 0,
    sysprint: '',
    systotal: '',

    // Core fields
    id: 'summary',
    line: 0,
    unit_id: '',

    // Entity codes
    ma_ngv: '',
    ma_kh: '',
    ma_nvbh: '',
    ma_ct: '',
    ma_vt: '',
    ma_bp: '',
    ma_cp0: 0,
    ma_dtt: '',
    ma_hd: '',
    ma_ku: '',
    ma_lsx: '',
    ma_phi: 0,
    ma_sp: '',
    ma_mr1: '',
    ma_mr2: '',
    ma_mr3: '',
    ma_vv: '',
    ma_unit: '',

    // Document fields
    ngay_ct: '',
    so_ct: '',
    status: '',
    dvt: '',
    he_so: 0,
    ngay_giao: '',

    // Transaction fields
    gia2: 0,
    tien2: totalTien2,
    so_luong: totalSoLuong,
    sl_xuat: totalSlXuat,
    sl_hd: totalSlHd,
    sl_dh: totalSlDh,
    sl_tl: totalSlTl,
    sl_cl: totalSlCl,

    // Display names
    ten_ngv: '',
    ten_kh: 'Tổng cộng',
    ten_vt: '',
    ten_ttct: '',
    ten_nvbh: '',

    // Summary flag
    isSummary: true
  };

  // Return summary row first, then regular data
  return [summaryRow, ...mockData];
};

/**
 * Custom hook for managing Sales Order Status Report data
 *
 * This hook provides functionality to fetch sales order status data
 * with mock support for testing and development purposes.
 */
export function useSalesOrderStatusData(searchParams: SearchFormValues): UseSalesOrderStatusReturn {
  const [data, setData] = useState<SalesOrderStatusItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<SalesOrderStatusResponse>(
        '/ban-hang/bao-cao-ban-hang/bao-cao-tinh-trang-don-hang/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
