const useActionHandlers = (refreshData: () => Promise<void>) => {
  const handleRefreshClick = () => {
    refreshData?.();
  };

  const handleFixedColumnsClick = () => {
    console.log('Fixed columns clicked');
  };

  const handleExportDataClick = () => {
    console.log('Export data clicked');
  };

  return {
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick
  };
};

export default useActionHandlers;
