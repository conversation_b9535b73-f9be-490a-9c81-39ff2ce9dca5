import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, type <PERSON><PERSON>TSCDCCDC, type TaiSanCoDinh } from '@/types/schemas';

/**
 * Hook for managing search field states in the bao-cao-chi-tiet-tang-giam-tscd feature
 *
 * @returns Object with states and setters for search fields
 */
export const useSearchFieldStates = () => {
  // BasicInfo tab states
  const [taiSan, setTaiSan] = useState<TaiSanCoDinh | null>(null);

  // DetailsTab states
  const [loaiTaiSan, setLoaiTaiSan] = useState<LoaiTSCDCCDC | null>(null);
  const [bo<PERSON><PERSON>, setBo<PERSON>han] = useState<BoPhan | null>(null);
  const [nhom1, setNhom1] = useState<any | null>(null);
  const [nhom2, setNhom2] = useState<any | null>(null);
  const [nhom3, setNhom3] = useState<any | null>(null);

  const searchFieldStates = {
    taiSan,
    setTai<PERSON><PERSON>,
    loai<PERSON><PERSON>San,
    set<PERSON>oai<PERSON>aiSan,
    bo<PERSON><PERSON>,
    setB<PERSON><PERSON><PERSON>,
    nhom1,
    setNhom1,
    nhom2,
    setNhom2,
    nhom3,
    setNhom3
  };
  return searchFieldStates;
};
