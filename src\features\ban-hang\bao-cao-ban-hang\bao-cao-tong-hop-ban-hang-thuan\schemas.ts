import { z } from 'zod';
import { dateLike } from '@/schemas/field-schemas';

export const searchSchema = z.object({
  // Main fields
  ngay_ct1: dateLike,
  ngay_ct2: dateLike,

  // General fields (non-search fields only)
  nh_kh1: z.string().optional(),
  nh_kh2: z.string().optional(),
  nh_kh3: z.string().optional(),
  rg_code: z.string().optional(),
  ma_lvt: z.string().optional(),
  ton_kho_yn: z.boolean().optional(),
  nh_vt1: z.string().optional(),
  nh_vt2: z.string().optional(),
  nh_vt3: z.string().optional(),
  ma_kho: z.string().optional(),
  ma_unit: z.string().optional(),
  nh_ct: z.string().optional(),
  loai_du_lieu: z.number().optional(),
  mau_bc: z.string().optional(),
  ma_gd: z.string().optional(),

  // Other fields (non-search fields only)
  so_ct1: z.string().optional(),
  so_ct2: z.string().optional(),
  dien_giai: z.string().optional()
});

export const initialValues = {
  // Main fields
  ngay_ct1: '',
  ngay_ct2: '',

  // General fields (non-search fields only)
  nh_kh1: '',
  nh_kh2: '',
  nh_kh3: '',
  rg_code: '',
  ma_lvt: '',
  ton_kho_yn: true,
  nh_vt1: '',
  nh_vt2: '',
  nh_vt3: '',
  ma_kho: '',
  ma_unit: '',
  nh_ct: 'BH1,BH2,BH3',
  loai_du_lieu: 1,
  mau_bc: '1',
  ma_gd: '',

  // Other fields (non-search fields only)
  so_ct1: '',
  so_ct2: '',
  dien_giai: ''
};
