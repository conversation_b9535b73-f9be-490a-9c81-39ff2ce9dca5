import { z } from 'zod';
import { dateLike } from '@/schemas/field-schemas';

export const searchSchema = z.object({
  // Main fields
  ngay_ct1: dateLike,
  ngay_ct2: dateLike,

  // General fields
  ma_kh: z.string().optional(),
  nh_kh1: z.string().optional(),
  nh_kh2: z.string().optional(),
  nh_kh3: z.string().optional(),
  rg_code: z.string().optional(),
  ma_vt: z.string().optional(),
  ma_lvt: z.string().optional(),
  ton_kho_yn: z.boolean().optional(),
  nh_vt1: z.string().optional(),
  nh_vt2: z.string().optional(),
  nh_vt3: z.string().optional(),
  ma_kho: z.string().optional(),
  ma_unit: z.string().optional(),
  nh_ct: z.string().optional(),
  loai_du_lieu: z.number().optional(),
  mau_bc: z.string().optional(),

  // Other fields
  ma_gd: z.string().optional(),
  tk_vt: z.string().optional(),
  tk_dt: z.string().optional(),
  tk_gv: z.string().optional(),
  ma_lo: z.string().optional(),
  ma_vi_tri: z.string().optional(),
  so_ct1: z.string().optional(),
  so_ct2: z.string().optional(),
  dien_giai: z.string().optional()
});

export const initialValues = {
  // Main fields
  ngay_ct1: '',
  ngay_ct2: '',

  // General fields
  ma_kh: '',
  nh_kh1: '',
  nh_kh2: '',
  nh_kh3: '',
  rg_code: '',
  ma_vt: '',
  ma_lvt: '',
  ton_kho_yn: false,
  nh_vt1: '',
  nh_vt2: '',
  nh_vt3: '',
  ma_kho: '',
  ma_unit: '',
  nh_ct: 'BH1,BH2,BH3',
  loai_du_lieu: 1,
  mau_bc: '1',

  // Other fields
  ma_gd: '',
  tk_vt: '',
  tk_dt: '',
  tk_gv: '',
  ma_lo: '',
  ma_vi_tri: '',
  so_ct1: '',
  so_ct2: '',
  dien_giai: ''
};
